import { useEffect } from 'react';

const CHUNK_SIZE = 500;

/**
 * Hook para virtualizar o carregamento de leis remotas em HTML.
 * Usa IntersectionObserver para carregar chunks de artigos conforme o usuário rola a página,
 * evitando o congelamento do navegador com documentos muito grandes.
 *
 * @param htmlRemoto O conteúdo HTML completo da lei.
 * @param containerId O ID do elemento contêiner onde o HTML será renderizado.
 * @param dependencies Array de dependências que, quando alterado, reinicia a virtualização.
 */
export function useLawVirtualization(
  htmlRemoto: string | null,
  containerId: string,
  dependencies: any[]
) {
  useEffect(() => {
    if (!htmlRemoto) return;

    const container = document.getElementById(containerId);
    if (!container) return;

    const hiddenHtml: string[] = [];
    let artCounter = 0;
    Array.from(container.children).forEach((node) => {
      const el = node as HTMLElement;
      if (el.hasAttribute('data-art')) artCounter++;
      if (artCounter > CHUNK_SIZE) {
        hiddenHtml.push(el.outerHTML);
        container.removeChild(el);
      }
    });

    if (!hiddenHtml.length) return;

    // --- Placeholder para manter altura aproximada ---
    const sampleEls = Array.from(container.querySelectorAll<HTMLElement>('[data-art]')).slice(0, 50);
    const avgH = sampleEls.reduce((sum, el) => sum + el.offsetHeight, 0) / (sampleEls.length || 1);
    const remainingArts = hiddenHtml.filter((h) => h.includes('data-art')).length;
    
    const placeholder = document.createElement('div');
    placeholder.id = 'placeholder-end';
    placeholder.style.height = `${Math.round(remainingArts * avgH)}px`;

    const sentinel = document.createElement('div');
    sentinel.id = 'sentinel-end';
    sentinel.style.height = '1px';

    container.appendChild(placeholder);
    container.appendChild(sentinel);

    const loadNextChunk = () => {
      let addedArts = 0;
      while (hiddenHtml.length && addedArts < CHUNK_SIZE) {
        const html = hiddenHtml.shift()!;
        placeholder.insertAdjacentHTML('beforebegin', html);
        const newEl = placeholder.previousElementSibling as HTMLElement;
        if (newEl && newEl.hasAttribute('data-art')) addedArts++;
      }
      // Ajusta altura do placeholder
      const reduce = addedArts * avgH;
      const newHeight = Math.max(0, (parseInt(placeholder.style.height) || 0) - reduce);
      placeholder.style.height = `${newHeight}px`;

      // Se ainda há artigos escondidos e o sentinel continua visível, carrega automaticamente o próximo chunk
      if (hiddenHtml.length) {
        const rect = sentinel.getBoundingClientRect();
        if (rect.top - window.innerHeight <= 1500) {
          requestAnimationFrame(loadNextChunk);
        }
      }

      if (!hiddenHtml.length || newHeight === 0) {
        observer.disconnect();
        placeholder.remove();
        sentinel.remove();
      }
    };

    const observer = new IntersectionObserver((entries) => {
      if (entries.some((e) => e.isIntersecting)) {
        loadNextChunk();
      }
    }, { rootMargin: '1500px' });

    observer.observe(sentinel);

    sentinel.addEventListener('manual-intersect', loadNextChunk);

    return () => {
      observer.disconnect();
      sentinel.removeEventListener('manual-intersect', loadNextChunk);
      // Garante que o sentinela e o placeholder sejam removidos na limpeza
      document.getElementById('sentinel-end')?.remove();
      document.getElementById('placeholder-end')?.remove();
      hiddenHtml.length = 0;
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [htmlRemoto, containerId, ...dependencies]);
}
