import React from 'react';
import { ChevronLeft } from 'lucide-react';
import { ThemeToggle } from '../ui/ThemeToggle';
import { HighlightController } from './HighlightController';
import { auth } from '../../firebase';
import type { LawHeaderProps } from '../../types/lawView';

export const LawHeader: React.FC<LawHeaderProps> = ({
  leiMeta,
  navigate,
  corAtiva,
  setCorAtiva,
  diminuir,
  aumentar,
  exportarGrifos,
  importarGrifos,
  toggleHierarchicalNav,
  onProfileClick,
  isHierarchicalNavVisible,
  hasHierarchicalContext,
}) => {
  return (
    <header className="flex items-center gap-3 p-4 border-b border-white/10 shrink-0">
      <button onClick={() => navigate(-1)} className="p-2 rounded hover:bg-white/10" aria-label="Voltar">
        <ChevronLeft />
      </button>
      <h2 className="font-semibold text-xl flex-1 truncate">{leiMeta.nome}</h2>
      <div className="flex items-center gap-2">
        {auth?.currentUser && (
          <button 
            onClick={onProfileClick} 
            className="text-xs px-2 py-1 rounded bg-primary text-primary-foreground shadow-lg"
          >
            Perfil
          </button>
        )}
        <HighlightController activeColor={corAtiva} onColorChange={setCorAtiva} />
        <button onClick={diminuir} className="p-2 rounded hover:bg-white/10" aria-label="Diminuir texto">
          A−
        </button>
        <button onClick={aumentar} className="p-2 rounded hover:bg-white/10" aria-label="Aumentar texto">
          A+
        </button>
        <button
          onClick={toggleHierarchicalNav}
          className="p-2 rounded hover:bg-white/10 text-sm"
          aria-label="Navegação hierárquica"
        >
          ☰
        </button>
        <button
          onClick={exportarGrifos}
          className="p-2 rounded hover:bg-white/10 text-sm"
          title="Exportar grifos"
        >
          ⭳
        </button>
        <label className="p-2 rounded hover:bg-white/10 text-sm cursor-pointer" title="Importar grifos">
          ⭱
          <input type="file" accept=".json" className="hidden" onChange={importarGrifos} />
        </label>
        <ThemeToggle />
        {!isHierarchicalNavVisible && hasHierarchicalContext && (
          <button
            onClick={toggleHierarchicalNav}
            className="p-2 rounded hover:bg-white/10 text-sm"
            title="Mostrar navegação hierárquica na tela"
            aria-label="Mostrar navegação hierárquica"
          >
            <span className="text-xs">⋯</span>
          </button>
        )}
      </div>
    </header>
  );
};
