import React, { useState } from 'react';
import type { TocItem } from '../../hooks/useTOC';

interface TableOfContentsProps {
  tocItems: TocItem[];
  onSelect: (id: string) => void;
}

export const TableOfContents: React.FC<TableOfContentsProps> = ({ tocItems, onSelect }) => {
  const [showToc, setShowToc] = useState(false);
  const [previewArt, setPreviewArt] = useState<number | null>(null);
  const [lastSelectedArt, setLastSelectedArt] = useState<number>(0);

  if (tocItems.length === 0) {
    return null;
  }

  // Garante que o artigo fique visível no viewport após o scroll virtual
  const ensureVisible = (id: string) => {
    setTimeout(() => {
      document.getElementById(id)?.scrollIntoView({ behavior: 'auto', block: 'start' });
    }, 20);
  };

  // Aplica a navegação imediatamente para acompanhar o slider
  const applyScroll = (index: number) => {
    const item = tocItems[index];
    if (item) {
      onSelect(item.id);
      ensureVisible(item.id);
      setLastSelectedArt(index);
    }
  };

  const handleSelect = (index: number) => {
    const item = tocItems[index];
    if (item) {
      onSelect(item.id);
      ensureVisible(item.id);
      setLastSelectedArt(index);
      // Removido setShowToc(false) para manter o componente aberto
    }
  };

  return (
    <>
      {/* TOC button */}
      <button
        onClick={() => {
          setPreviewArt(lastSelectedArt);
          setShowToc((v) => !v);
        }}
        className="fixed bottom-6 right-6 z-40 w-8 h-8 flex items-center justify-center rounded-full bg-neutral-800 text-white shadow-lg border border-white/20 hover:bg-neutral-700 transition-colors"
        aria-label="Sumário"
      >
        <svg width="18" height="18" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
          <circle cx="10" cy="10" r="8" stroke="white" strokeWidth="2" fill="none" />
          <rect x="8.5" y="8.5" width="3" height="3" rx="1" fill="white" />
        </svg>
      </button>

      {/* TOC overlay horizontal */}
      {showToc && (
        <div
          className="fixed z-50 inset-0 flex items-end justify-end"
          style={{ pointerEvents: 'auto' }}
          onClick={() => setShowToc(false)}
        >
          <div
            className="relative flex flex-col items-center"
            style={{ bottom: 72, right: 24, pointerEvents: 'auto', position: 'absolute' }}
            onClick={e => e.stopPropagation()}
          >

            <div className="relative w-56 mb-2 h-8 flex items-center justify-center custom-scrollbar">
              <input
                type="range"
                min={0}
                max={tocItems.length - 1}
                value={previewArt ?? lastSelectedArt}
                onMouseOver={(e) => setPreviewArt(Number((e.target as HTMLInputElement).value))}
                onMouseMove={(e) => setPreviewArt(Number((e.target as HTMLInputElement).value))}
                onMouseOut={() => setPreviewArt(null)}
                onChange={(e) => {
                  const index = Number((e.target as HTMLInputElement).value);
                  setPreviewArt(index);
                  applyScroll(index);
                }}
                
                onMouseUp={(e) => {
                  const index = Number((e.target as HTMLInputElement).value);
                  handleSelect(index);
                }}
                onTouchMove={(e) => {
                  const index = Number((e.target as HTMLInputElement).value);
                  setPreviewArt(index);
                  applyScroll(index);
                }}
                onTouchEnd={(e) => {
                  const index = Number((e.target as HTMLInputElement).value);
                  handleSelect(index);
                }}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
              />
              <div
                className="absolute text-white text-xs rounded-lg px-2 py-1 -top-8 pointer-events-none whitespace-nowrap shadow-lg bg-primary/80 transition-opacity duration-150"
                style={{
                  left: `calc(${(previewArt ?? lastSelectedArt) / (tocItems.length - 1) * 100}% - 2rem)`,
                  opacity: previewArt !== null ? 1 : 0,
                }}
              >
                {tocItems[previewArt ?? lastSelectedArt]?.title}
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};
