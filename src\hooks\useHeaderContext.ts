import { useState, useEffect, RefObject } from 'react';

export function useHeaderContext(leiRef: RefObject<HTMLElement>, dependency: any) {
  const [contextoCabecalho, setContextoCabecalho] = useState<string | null>(null);
  const [identLinha, setIdentLinha] = useState<string | null>(null);
  const [showHeadHint, setShowHeadHint] = useState(true);

  useEffect(() => {
    if (!leiRef.current) return;

    const handleScroll = () => {
      if (!leiRef.current) return;

      const cabecalhos = Array.from(leiRef.current.querySelectorAll('.L, .T, .C, .S, .SS'));
      const viewportY = window.innerHeight / 4;

      let bestMatch: Element | null = null;

      for (const el of cabecalhos) {
        const rect = el.getBoundingClientRect();
        if (rect.top < viewportY) {
          bestMatch = el;
        } else {
          break;
        }
      }

      if (bestMatch) {
        const clone = bestMatch.cloneNode(true) as HTMLElement;
        clone.querySelector('span')?.remove();
        const fullText = clone.textContent?.trim() || '';
        const [ident, ...rest] = fullText.split(/\s*-\s*/);
        setContextoCabecalho(rest.join(' - '));
        setIdentLinha(ident);
      } else {
        setContextoCabecalho(null);
        setIdentLinha(null);
      }
    };

    const container = leiRef.current;
    window.addEventListener('scroll', handleScroll, { passive: true });
    handleScroll(); // Initial check

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [dependency, leiRef]);

  return { contextoCabecalho, identLinha, showHeadHint, setShowHeadHint };
}
