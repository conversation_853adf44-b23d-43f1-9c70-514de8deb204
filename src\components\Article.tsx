import React, { useState, useEffect } from "react";
import { Highlight<PERSON>, Info } from "lucide-react";
import toast from "react-hot-toast";

import { explainLawBrief, fetchJurisprudence } from "../services";

interface Props {
  id: string; // ex: "art1"
  text: string; // conteúdo completo do artigo


  removeHighlight: (id: string) => Promise<void>;
}

function ArticleComponent({ id, text, removeHighlight }: Props) {
  const [loading, setLoading] = useState(false);
  const [loadingJuris, setLoadingJuris] = useState(false);
  const [explanation, setExplanation] = useState<string | null>(null);
  const [juris, setJuris] = useState<string | null>(null);

  // Limpamos estados pesados quando o artigo muda (virtual list recicla componentes)
  useEffect(() => {
    setExplanation(null);
    setJuris(null);
  }, [id]);



  async function handleExplain() {
    try {
      setLoading(true);
      setExplanation(null);
      const answer = await explainLawBrief(text);
      setExplanation(answer || "Sem resposta");
    } catch (err: any) {
      console.error(err);
      toast.error(err.message || "Erro ao obter explicação IA");
    } finally {
      setLoading(false);
    }
  }

  async function handleJurisprudence() {
    try {
      setLoadingJuris(true);
      setJuris(null);
      const answer = await fetchJurisprudence(text);
      setJuris(answer || "Sem resultado");
    } catch (err: any) {
      console.error(err);
      toast.error(err.message || "Erro ao obter jurisprudência");
    } finally {
      setLoadingJuris(false);
    }
  }



  return (
    <section id={id} className="sticky top-0 bg-background py-2 z-10">
      <div className="flex items-center gap-2 mb-1">
        <Highlighter size={16} className="opacity-70" />
        <span className="font-semibold">{id.toUpperCase()}</span>
        <button
          onClick={handleExplain}
          className="ml-auto text-sm flex items-center gap-1 opacity-80 hover:opacity-100"
        >
          <Info size={14} /> Explicar
        </button>
        <button
          onClick={handleJurisprudence}
          className="text-sm flex items-center gap-1 ml-2 opacity-80 hover:opacity-100"
        >
          Jurisprudência
        </button>
      </div>

      <p className="whitespace-pre-wrap leading-relaxed" dangerouslySetInnerHTML={{ __html: text }}></p>

      {loading && <p className="text-xs opacity-60 mt-1">Consultando IA...</p>}
      {explanation && (
        <blockquote
          className="mt-2 border-l-4 border-primary pl-3 text-sm bg-white/5 rounded-md"
          dangerouslySetInnerHTML={{ __html: explanation.replace(/\n/g, '<br/>') }}
        />
      )}

      {loadingJuris && <p className="text-xs opacity-60 mt-1">Buscando jurisprudência...</p>}
      {juris && (
        <blockquote
          className="mt-2 border-l-4 border-yellow-400 pl-3 text-sm bg-white/5 rounded-md"
          dangerouslySetInnerHTML={{ __html: juris.replace(/\n/g, '<br/>') }}
        />
      )}
    </section>
  );
}

export const Article = React.memo(ArticleComponent);

// Mantém compatibilidade com possíveis imports default anteriores
export type { Props as ArticleProps }; // Caso outros arquivos importem o tipo 