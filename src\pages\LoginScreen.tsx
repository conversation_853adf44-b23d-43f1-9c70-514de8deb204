import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import toast from "react-hot-toast";
import { STRIPE_BACKEND_URL, getStripePublishableKey } from "../utils/env";
import { fetchJson } from "../utils/http";
import {
  GoogleAuthProvider,
  signInWithPopup,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut,
} from "firebase/auth";
import { auth } from "../firebase";
import { getFunctions, httpsCallable } from "firebase/functions";
import { ThemeToggle } from "../components/ui/ThemeToggle";
import Modal from "../components/ui/Modal";
import { loadStripe } from '@stripe/stripe-js';

// Initialize Stripe with environment variable
let stripePromise: Promise<any> | null = null;

function getStripePromise() {
  if (!stripePromise) {
    try {
      const publishableKey = getStripePublishableKey();
      stripePromise = loadStripe(publishableKey);
    } catch (error) {
      console.error('Failed to initialize Stripe:', error);
      toast.error('Stripe não está configurado corretamente');
      return null;
    }
  }
  return stripePromise;
}

interface LoginProps {
  minimal?: boolean;
  onGuestContinue?: () => void;
  onLoginSuccess?: () => void;
}

export function LoginScreen({ minimal = false, onGuestContinue, onLoginSuccess }: LoginProps) {
  const navigate = useNavigate();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [isRegistering, setIsRegistering] = useState(false);

  // State for modal flow
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);
  const [selectedPriceId, setSelectedPriceId] = useState<string | null>(null);

  useEffect(() => {
    // Only redirect if on the main login page, not in a modal
    if (auth?.currentUser && !minimal) {
      navigate("/home", { replace: true });
    }
  }, [minimal, navigate]);

  const handleAuthSuccess = () => {
    localStorage.removeItem('guest_mode');
    localStorage.removeItem('ia_credits_left');
    if (onLoginSuccess) {
      onLoginSuccess();
    } else {
      navigate("/home", { replace: true });
    }
  };

  async function handleGoogle() {
    if (!auth) return toast.error("Firebase não configurado");
    try {
      setLoading(true);
      const provider = new GoogleAuthProvider();
      await signInWithPopup(auth, provider);
      toast.success("Login realizado!");
      handleAuthSuccess();
    } catch (err: any) {
      toast.error(err.message || 'Falha no login');
    } finally {
      setLoading(false);
    }
  }

  async function handleEmailPass(e: React.FormEvent) {
    e.preventDefault();
    if (!auth) return toast.error("Firebase não configurado");
    try {
      setLoading(true);
      if (isRegistering) {
        await createUserWithEmailAndPassword(auth, email, password);
        toast.success("Conta criada!");
      } else {
        await signInWithEmailAndPassword(auth, email, password);
        toast.success("Login realizado!");
      }
      handleAuthSuccess();
    } catch (err: any) {
      const code = err.code as string | undefined;
      if (code === 'auth/admin-restricted-operation') {
        toast.error('Cadastro desativado. Contate o administrador.');
      } else {
        toast.error(err.message || 'Falha no login');
      }
    } finally {
      setLoading(false);
    }
  }

  async function handleGuest() {
    localStorage.setItem("guest_mode", "1");
    if (!localStorage.getItem('ia_credits_left') || parseInt(localStorage.getItem('ia_credits_left')||'0',10)<=0) {
      localStorage.setItem('ia_credits_left', '5');
    }
    // Clean up old guest highlights (both old 'anon' and new 'guest' keys)
    Object.keys(localStorage).forEach((k) => {
      if (k.startsWith('grifos-anon-') || k.startsWith('grifos-guest-')) {
        localStorage.removeItem(k);
      }
    });
    if (auth) {
      try { await signOut(auth); } catch {}
    }
    if (!minimal) {
      navigate("/home", { replace: true });
    } else {
      onGuestContinue?.();
    }
  }

  async function startCheckout(priceId: string) {
    setLoading(true);
    try {
      const backendUrl = `${STRIPE_BACKEND_URL}/create-checkout-session`;

      const successUrl = `${window.location.origin}/home`;
      const cancelUrl = window.location.origin;

      // Chama o backend local via fetch
      const session = await fetchJson<{ id: string }>(backendUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ priceId, successUrl, cancelUrl }),
        timeout: 10000,
      });
      const sessionId = session.id;

      // Redireciona para o checkout do Stripe.
      const stripePromiseInstance = getStripePromise();
      if (!stripePromiseInstance) {
        toast.error("Stripe não está configurado corretamente.");
        return;
      }

      const stripe = await stripePromiseInstance;
      if (stripe) {
        await stripe.redirectToCheckout({ sessionId });
      } else {
        toast.error("Não foi possível carregar a página de pagamento.");
      }
    } catch (error: any) {
      console.error("Erro ao criar sessão de checkout:", error);
      const msg =
        error?.message === 'Failed to fetch'
          ? 'Não foi possível conectar ao servidor de pagamentos.'
          : error.message || 'Falha ao iniciar o processo de assinatura.';
      toast.error(msg);
    } finally {
      setLoading(false);
    }
  }

  async function handleSubscription(priceId: string) {
    if (!auth) return toast.error("Serviço de autenticação indisponível.");
    if (auth.currentUser) {
      await startCheckout(priceId);
    } else {
      setSelectedPriceId(priceId);
      setIsLoginModalOpen(true);
    }
  }

  const containerClass = minimal
    ? "flex flex-col items-center justify-center p-4 max-h-[95vh] overflow-auto"
    : "min-h-screen flex flex-col items-center justify-center p-4";

  // The content of the login form, to be reused in the page and the modal
  const loginFormContent = (
    <>
      <h2 className="text-2xl font-semibold mb-6">{isRegistering ? "Criar Conta" : "Acessar Conta"}</h2>
      <button
        onClick={handleGoogle}
        disabled={loading}
        className="w-64 py-2 mb-4 rounded-md bg-white text-neutral-900 shadow flex items-center justify-center gap-2 hover:bg-white/90"
      >
        <img src="https://www.gstatic.com/firebasejs/ui/2.0.0/images/auth/google.svg" alt="Google" className="w-5 h-5" />
        Entrar com Google
      </button>
      <form onSubmit={handleEmailPass} className="w-64 grid gap-3">
        <input type="email" placeholder="Email" value={email} onChange={(e) => setEmail(e.target.value)} className="px-3 py-2 rounded-md bg-white/5 border border-white/20" required />
        <input type="password" placeholder="Senha" value={password} onChange={(e) => setPassword(e.target.value)} className="px-3 py-2 rounded-md bg-white/5 border border-white/20" required />
        <button type="submit" disabled={loading} className="py-2 rounded-md bg-primary/80 hover:bg-primary transition-colors">
          {isRegistering ? "Registrar" : "Entrar"}
        </button>
      </form>
      <button onClick={() => setIsRegistering(!isRegistering)} className="mt-4 text-sm opacity-80 hover:opacity-100">
        {isRegistering ? "Já tem uma conta? Entrar" : "Não tem uma conta? Registrar"}
      </button>
      <hr className="my-6 w-64 border-white/10" />
      <button onClick={handleGuest} className="underline text-sm opacity-80 hover:opacity-100">
        Continuar como Convidado
      </button>
    </>
  );

  if (minimal) {
    return loginFormContent;
  }

  return (
    <div className={containerClass}>
      <div className="absolute top-4 right-4">
        <ThemeToggle />
      </div>
      
      {loginFormContent}

      {/* Seção de Planos de Assinatura */}
      <div className="mt-8 pt-6 border-t border-white/10 w-full max-w-2xl text-center">
        <h3 className="text-xl font-semibold mb-4">Nossos Planos</h3>
        <div className="flex flex-col md:flex-row gap-4 justify-center">
          <div className="border border-white/20 rounded-lg p-4 flex-1 flex flex-col">
            <h4 className="text-lg font-bold">PLANO 1</h4>
            <p className="text-2xl font-semibold my-2">R$9,99<span className="text-sm font-normal">/MÊS</span></p>
            <ul className="text-left text-sm space-y-2 opacity-80 flex-grow">
              <li>✓ Grifos salvos na nuvem</li>
              <li>✓ App celular offline com sincronização</li>
              <li>✓ Possibilidade de API Key particular para IA</li>
            </ul>
            <button onClick={() => handleSubscription('price_1Ri1PbGaOqxUlEi69DL9vmnl')} className="mt-4 py-2 rounded-md bg-primary/80 hover:bg-primary transition-colors w-full">
              Assinar
            </button>
          </div>
          <div className="border border-white/20 rounded-lg p-4 flex-1 flex flex-col">
            <h4 className="text-lg font-bold">PLANO 2</h4>
            <p className="text-2xl font-semibold my-2">R$19,99<span className="text-sm font-normal">/MÊS</span></p>
            <ul className="text-left text-sm space-y-2 opacity-80 flex-grow">
              <li>✓ Tudo do Plano 1</li>
              <li>✓ Pacote de tokens para IAs (Gemini/GPT/Claude)</li>
            </ul>
            <button onClick={() => handleSubscription('price_1PjL8PGaOqxUlEi65sM2pYtP')} className="mt-4 py-2 rounded-md bg-secondary/80 hover:bg-secondary transition-colors w-full">
              Assinar
            </button>
          </div>
        </div>
      </div>

      <Modal isOpen={isLoginModalOpen} onClose={() => setIsLoginModalOpen(false)}>
        <LoginScreen 
          minimal
          onLoginSuccess={() => {
            setIsLoginModalOpen(false);
            if (selectedPriceId) {
              startCheckout(selectedPriceId);
            }
          }}
          onGuestContinue={() => setIsLoginModalOpen(false)}
        />
      </Modal>
    </div>
  );
} 