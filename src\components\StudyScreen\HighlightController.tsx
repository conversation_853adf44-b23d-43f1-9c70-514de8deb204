import React, { useState } from 'react';
import { Highlighter } from 'lucide-react';
import { ColorPalette } from '../ui/ColorPalette';

const CORES = ["#00FFFF", "#00FF7F", "#FF00FF", "#FFD700"];

interface Props {
  activeColor: string;
  onColorChange: (color: string) => void;
}

export function HighlightController({ activeColor, onColorChange }: Props) {
  const [showPalette, setShowPalette] = useState(true);

  return (
    <div className="relative">
      <button 
        onClick={() => setShowPalette(prev => !prev)} 
        className="p-2 rounded hover:bg-white/10"
        aria-label="Selecionar cor do grifo"
      >
        <Highlighter size={20} color={activeColor} />
      </button>

      {showPalette && (
        <ColorPalette 
          cores={CORES}
          corAtiva={activeColor}
          onChange={onColorChange}
        />
      )}
    </div>
  );
}
