const fs = require('fs');
const path = require('path');

const CACHE_FILE = path.join(__dirname, 'laws-cache.json');
let cache = {};

const ONE_DAY_MS = 24 * 60 * 60 * 1000;

function isCacheValid(timestamp) {
  return Date.now() - timestamp < ONE_DAY_MS;
}

function readCache() {
  try {
    if (fs.existsSync(CACHE_FILE)) {
      const raw = fs.readFileSync(CACHE_FILE, 'utf8');
      cache = JSON.parse(raw);
    }
  } catch (error) {
    console.error('Error reading cache:', error);
  }
}

function writeCache() {
  try {
    fs.writeFileSync(CACHE_FILE, JSON.stringify(cache, null, 2));
  } catch (error) {
    console.error('Error writing cache:', error);
  }
}

function getFromCache(id) {
  return cache[id];
}

function setToCache(id, law) {
  cache[id] = law;
  writeCache();
}

module.exports = {
  readCache,
  writeCache,
  getFromCache,
  setToCache,
  isCacheValid
};
