export async function fetchJson<T>(
  url: string,
  options: (RequestInit & { timeout?: number }) = {}
): Promise<T> {
  const { timeout, signal, ...rest } = options;
  const controller = new AbortController();
  const id = timeout ? setTimeout(() => controller.abort(), timeout) : undefined;
  try {
    const res = await fetch(url, { ...rest, signal: signal ?? controller.signal });
    if (!res.ok) {
      const text = await res.text().catch(() => '');
      throw new Error(`HTTP ${res.status}${text ? `: ${text}` : ''}`);
    }
    return (await res.json()) as T;
  } finally {
    if (id) clearTimeout(id);
  }
}

