import { useEffect } from "react";
import type { Highlight } from "./useHighlights";
import { applySingleNodeHighlight, applyComplexHighlight } from "../utils/highlightUtils";

// Debug logging utility
const debugLog = (message: string, ...args: any[]) => {
  if (import.meta.env.DEV) {
    console.debug(`[useHighlightRenderer] ${message}`, ...args);
  }
};

interface UseHighlightRendererProps {
  containerId: string;
  highlights: Highlight[];
  getNodeFromPath: (path: number[], root: Node) => Node | null;
  onRemove?: (id: string) => void;
}

/**
 * Hook customizado para renderizar highlights de forma otimizada
 */
export function useHighlightRenderer({
  containerId,
  highlights,
  getNodeFromPath,
  onRemove
}: UseHighlightRendererProps) {
  
  useEffect(() => {
    const container = document.getElementById(containerId);
    if (!container) return;

    // Limpa grifos órfãos do DOM (que não estão mais na lista de highlights)
    const existingMarks = container.querySelectorAll('mark[data-grifo]');
    const highlightIds = new Set(highlights.map(h => h.id));

    existingMarks.forEach((mark) => {
      const markId = mark.getAttribute('data-grifo');
      if (markId && !highlightIds.has(markId)) {
        // Remove grifo órfão do DOM
        const textContent = mark.textContent || '';
        const textNode = document.createTextNode(textContent);
        mark.parentNode?.replaceChild(textNode, mark);
        debugLog(`Removed orphaned highlight from DOM: ${markId}`);
      }
    });

    if (!highlights.length) return;

    // Wait for DOM to be ready - sometimes content loads after highlights
    const ensureContentReady = () => {
      // For virtualized content, also check for article sections
      const articleSections = Array.from(document.querySelectorAll('section[id^="art"]'));

      // Use the container if it has content, otherwise use the document body for virtualized content
      const targetContainer = container.children.length > 0 || container.textContent?.trim() ? container : document.body;

      // If container is still empty, wait a bit more
      if (!targetContainer.textContent?.trim() && targetContainer === container) {
        setTimeout(ensureContentReady, 100);
        return;
      }

      processHighlights(targetContainer);
    };

    const processHighlights = (targetContainer: Element) => {
      // 1) Coleta de grifos já presentes no DOM (uma só varredura)
      const existingTexts = new Set(
        Array.from(targetContainer.querySelectorAll<HTMLElement>("mark[data-grifo]"))
          .map((m) => m.dataset.grifo || '')
      );

      // 2) Filtra somente os grifos que ainda não estão pintados
      const pending = highlights.filter((h) => h.id && !existingTexts.has(h.id));
      
      if (!pending.length) return;

      // 3) Aplica imediatamente para novos grifos, em lotes para muitos grifos
      let index = 0;
      let cancelled = false;
      const failedHighlights: Highlight[] = [];

      const applyHighlight = (h: Highlight) => {
        // Tenta primeiro aplicação simples (nó único)
        if (applySingleNodeHighlight(h, targetContainer, onRemove)) {
          return true;
        }

        // Se falhou, tenta aplicação complexa
        if (applyComplexHighlight(h, targetContainer, onRemove)) {
          return true;
        }

        return false;
      };

      const processBatch = (deadline?: IdleDeadline) => {
        const BATCH_SIZE = pending.length <= 5 ? pending.length : 50; // Se poucos grifos, aplica todos imediatamente
        let processed = 0;
        let successful = 0;

        while (index < pending.length && processed < BATCH_SIZE) {
          // Se estivermos num deadline de idle, respeita timeRemaining()
          if (deadline && deadline.timeRemaining() < 5 && pending.length > 5) break;

          const result = applyHighlight(pending[index]);
          if (result) {
            successful++;
          } else {
            // Track failed highlights for retry
            failedHighlights.push(pending[index]);
          }

          index++;
          processed++;
        }

        debugLog(`Batch processado: ${processed} grifos, ${successful} sucessos`);

        if (index < pending.length && !cancelled) {
          if (typeof (window as any).requestIdleCallback === "function") {
            (window as any).requestIdleCallback(processBatch);
          } else {
            setTimeout(processBatch, 16); // próximo frame ~60fps
          }
        } else if (index >= pending.length) {
          debugLog(`Processamento completo: ${pending.length} grifos processados, ${successful} sucessos, ${failedHighlights.length} falhas`);

          // Retry failed highlights after a short delay
          if (failedHighlights.length > 0) {
            debugLog(`Tentando reaplicar ${failedHighlights.length} grifos que falharam`);
            setTimeout(() => {
              if (!cancelled) {
                failedHighlights.forEach(h => applyHighlight(h));
              }
            }, 500);
          }
        }
      };

      // Para poucos grifos (≤5), aplica imediatamente sem delay
      if (pending.length <= 5) {
        processBatch();
      } else if (typeof (window as any).requestIdleCallback === "function") {
        (window as any).requestIdleCallback(processBatch);
      } else {
        setTimeout(processBatch, 0);
      }

      // Return cleanup function
      return () => {
        cancelled = true;
      };
    };

    // Start the content ready check
    ensureContentReady();

    // Return cleanup function for useEffect
    return () => {
      // Any cleanup logic if needed
    };
  }, [containerId, highlights, getNodeFromPath, onRemove]);
}
