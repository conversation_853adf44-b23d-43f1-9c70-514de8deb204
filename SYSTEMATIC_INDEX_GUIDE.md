# Visualizador de Índice Sistemático - Guia de Uso

## Visão Geral

O Visualizador de Índice Sistemático é uma nova funcionalidade que permite aos usuários navegar facilmente pela estrutura hierárquica das leis, similar ao índice mostrado na imagem de referência.

## Funcionalidades Implementadas

### 1. Hook useSystematicIndex
- **Localização**: `src/hooks/useSystematicIndex.ts`
- **Função**: Analisa o HTML das leis e extrai a estrutura hierárquica
- **Detecta**: Livros, Títulos, Capítulos, Seções, Subseções e Artigos
- **Padrões suportados**:
  - `LIVRO I`, `PARTE I`
  - `TÍTULO I`, `TÍTULO II`
  - `CAPÍTULO I`, `CAPÍTULO II`
  - `SEÇÃO I`, `SEÇÃO II`
  - `SUBSEÇÃO I`, `SUBSEÇÃO II`
  - `Art. 1º`, `Art. 2º`

### 2. Componente SystematicIndex
- **Localização**: `src/components/SystematicIndex.tsx`
- **Função**: Modal que exibe o índice sistemático
- **Características**:
  - Interface hierárquica expansível
  - Cores diferenciadas por tipo de elemento
  - Navegação por clique
  - Contador de itens filhos
  - Estado de carregamento
  - Design responsivo

### 3. Integração no LawCard
- **Localização**: `src/components/LawCard.tsx`
- **Função**: Botão de índice sistemático nos cartões das leis
- **Características**:
  - Ícone de documento (FileText)
  - Aparece no hover
  - Cor azul para diferenciação

## Como Usar

### 1. Na Tela Inicial
1. Passe o mouse sobre qualquer cartão de lei
2. Clique no ícone azul de documento (📄) que aparece no canto superior direito
3. O modal do índice sistemático será aberto

### 2. Botão de Teste
- Um botão "Teste Índice" foi adicionado no cabeçalho para demonstração
- Clique nele para ver o índice sistemático com dados de exemplo do CDC

### 3. Navegação no Índice
- **Expandir/Recolher**: Clique no elemento para expandir ou recolher seus filhos
- **Navegar**: Clique na seta (→) à direita para navegar diretamente para a seção
- **Cores**: Cada tipo de elemento tem uma cor específica:
  - 🔴 Livro/Parte (vermelho)
  - 🟢 Título (verde)
  - 🟣 Capítulo (roxo)
  - 🟠 Seção (laranja)
  - 🟡 Subseção (amarelo)
  - 🔵 Artigo (azul)

## Estrutura Técnica

### Tipos de Dados
```typescript
interface SystematicIndexItem {
  id: string;
  title: string;
  type: 'livro' | 'titulo' | 'capitulo' | 'secao' | 'subsecao' | 'artigo';
  level: number;
  children?: SystematicIndexItem[];
  element?: HTMLElement;
}
```

### Detecção de Padrões
O sistema usa expressões regulares para detectar diferentes tipos de elementos:
- Padrões principais: `LIVRO I`, `TÍTULO I`, etc.
- Padrões alternativos: `LIVRO - Descrição`, `TÍTULO - Descrição`
- Classes CSS: `.heading-livro`, `.heading-titulo`, etc.

### Estados de Carregamento
- Indicador de carregamento enquanto a lei é processada
- Mensagem de "estrutura não encontrada" para leis sem hierarquia detectável
- Contador total de itens encontrados

## Melhorias Futuras

1. **Busca no Índice**: Adicionar campo de busca para filtrar itens
2. **Favoritos**: Permitir marcar seções favoritas
3. **Histórico**: Manter histórico de navegação
4. **Exportação**: Exportar índice como PDF ou texto
5. **Personalização**: Permitir personalizar cores e layout

## Testes

### Dados de Teste
O sistema inclui dados de teste do Código de Defesa do Consumidor para demonstração.

### Como Testar
1. Clique no botão "Teste Índice" na tela inicial
2. Explore a estrutura hierárquica
3. Teste a navegação clicando nos elementos
4. Verifique a responsividade em diferentes tamanhos de tela

## Compatibilidade

- ✅ Desktop
- ✅ Tablet
- ✅ Mobile
- ✅ Modo escuro/claro
- ✅ Todos os navegadores modernos

## Arquivos Modificados

1. `src/hooks/useSystematicIndex.ts` - Novo hook para extração da estrutura
2. `src/components/SystematicIndex.tsx` - Novo componente modal
3. `src/components/LawCard.tsx` - Adicionado botão de índice
4. `src/pages/HomeScreen.tsx` - Integração do modal e botão de teste
5. `src/hooks/useRemoteLaw.ts` - Adicionados dados de teste

## Conclusão

O Visualizador de Índice Sistemático oferece uma maneira intuitiva e eficiente de navegar pela estrutura complexa das leis brasileiras, melhorando significativamente a experiência do usuário ao estudar legislação.
