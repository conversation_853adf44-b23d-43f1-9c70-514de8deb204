import { useState, useEffect, useRef, useCallback } from 'react';
import toast from 'react-hot-toast';

const LIMITE_CREDITOS = 5;

export function useGuestMode(grifosRemaining: number) {
  const guestMode = (typeof localStorage !== 'undefined' && localStorage.getItem('guest_mode') === '1');

  const [creditsLeft, setCreditsLeft] = useState<number>(() => {
    if (!guestMode) return Infinity;
    const raw = typeof localStorage !== 'undefined' ? localStorage.getItem('ia_credits_left') : null;
    let initial: number;
    if (raw !== null) {
      const parsed = parseInt(raw, 10);
      if (isNaN(parsed) || parsed > LIMITE_CREDITOS) {
        initial = LIMITE_CREDITOS;
      } else {
        initial = parsed;
      }
    } else {
      initial = LIMITE_CREDITOS;
    }
    if (typeof localStorage !== 'undefined') localStorage.setItem('ia_credits_left', String(initial));
    return initial;
  });

  const [showPaywall, setShowPaywall] = useState(false);
  const [paywallDismissed, setPaywallDismissed] = useState<boolean>(() => {
    if (typeof localStorage === 'undefined') return false;
    const dismissed = localStorage.getItem('paywall_dismissed') === '1';
    if (dismissed) {
        // Se já foi dispensado, mas os créditos acabaram, forçamos a reexibição
        const credits = parseInt(localStorage.getItem('ia_credits_left') || '0', 10);
        if(credits <= 0) return false;
    }
    return dismissed;
  });

  const highlightDenialsRef = useRef(0);

  const consumeCredit = useCallback((): boolean => {
    if (!guestMode) return true;

    if (creditsLeft <= 0) {
      toast.error('Créditos esgotados! Faça login para continuar.');
      if (paywallDismissed) {
        setPaywallDismissed(false);
        localStorage.setItem('paywall_dismissed', '0');
      }
      setShowPaywall(true);
      return false;
    }

    const newVal = creditsLeft - 1;
    setCreditsLeft(newVal);
    if (typeof localStorage !== 'undefined') localStorage.setItem('ia_credits_left', String(newVal));

    if (newVal === 0 && !showPaywall) {
      setTimeout(() => {
        if (paywallDismissed) {
            setPaywallDismissed(false);
            localStorage.setItem('paywall_dismissed', '0');
        }
        setShowPaywall(true);
      }, 300);
    }
    return true;
  }, [guestMode, creditsLeft, paywallDismissed, showPaywall]);

  // Abre paywall se acabar créditos OU grifos
  useEffect(() => {
    if (guestMode && (creditsLeft <= 0 || grifosRemaining <= 0) && !paywallDismissed) {
      setShowPaywall(true);
    }
  }, [guestMode, creditsLeft, grifosRemaining, paywallDismissed]);

  // Reseta contagem sempre que paywall aparece novamente
  useEffect(() => {
    if (showPaywall) {
      highlightDenialsRef.current = 0;
    }
  }, [showPaywall]);

  // Travar scroll do body enquanto paywall visível
  useEffect(() => {
    if (showPaywall) {
      const original = document.body.style.overflow;
      document.body.style.overflow = 'hidden';
      return () => { document.body.style.overflow = original; };
    }
  }, [showPaywall]);

  const handlePaywallDismiss = () => {
      setShowPaywall(false);
      setPaywallDismissed(true);
      if(typeof localStorage !== 'undefined') {
          localStorage.setItem('paywall_dismissed', '1');
      }
  }

  return {
    guestMode,
    creditsLeft,
    showPaywall,
    setShowPaywall,
    paywallDismissed,
    handlePaywallDismiss,
    highlightDenialsRef,
    consumeCredit,
  };
}
