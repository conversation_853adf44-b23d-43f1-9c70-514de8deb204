import { useState } from 'react';
import toast from 'react-hot-toast';
import { explainLawBrief, explainLawWithExample, fetchJurisprudence } from '../services';
import type { Article } from '../types';

export type DialogArticle = Article & { title: string };

export function useAIDialog(consumeCredit: () => boolean) {
  const [dialogArt, setDialogArt] = useState<DialogArticle | null>(null);
  const [loadingExp, setLoadingExp] = useState(false);
  const [loadingJur, setLoadingJur] = useState(false);
  const [dialogExp, setDialogExp] = useState<string | null>(null);
  const [dialogJur, setDialogJur] = useState<string | null>(null);

  const openDialog = (article: DialogArticle) => {
    setDialogArt(article);
    setDialogExp(null);
    setDialogJur(null);
  };

  const closeDialog = () => {
    setDialogArt(null);
  };

  const handleExplain = async () => {
    if (!dialogArt) return;
    if (!consumeCredit()) return;
    try {
      setLoadingExp(true);
      setDialogExp(null);
      const ans = await explainLawBrief(dialogArt.texto);
      setDialogExp(ans || "Sem resposta");
    } catch (err: any) {
      console.error(err);
      toast.error(err.message || "Erro ao obter explicação IA");
    } finally {
      setLoadingExp(false);
    }
  };

  const handleExplainWithExample = async () => {
    if (!dialogArt) return;
    if (!consumeCredit()) return;
    try {
      setLoadingExp(true);
      setDialogExp(null);
      const ans = await explainLawWithExample(dialogArt.texto);
      setDialogExp(ans || "Sem resposta");
    } catch (err: any) {
      console.error(err);
      toast.error(err.message || "Erro ao obter explicação com exemplo");
    } finally {
      setLoadingExp(false);
    }
  };

  const handleJurisprudence = async () => {
    if (!dialogArt) return;
    if (!consumeCredit()) return;
    try {
      setLoadingJur(true);
      setDialogJur(null);
      const ans = await fetchJurisprudence(dialogArt.texto);
      setDialogJur(ans || "Sem resposta");
    } catch (err: any) {
      console.error(err);
      toast.error(err.message || "Erro ao buscar jurisprudência");
    } finally {
      setLoadingJur(false);
    }
  };

  return {
    dialogArt,
    loadingExp,
    loadingJur,
    dialogExp,
    dialogJur,
    openDialog,
    closeDialog,
    handleExplain,
    handleExplainWithExample,
    handleJurisprudence,
  };
}
