import { useState, useEffect } from 'react';
import { LeiMeta } from '../data/leis';
import { fetchLawHtml, improveFormatting } from '../utils';

export function useRemoteLaw(leiId: string | undefined, leiMeta: LeiMeta | undefined) {
  const [htmlRemoto, setHtmlRemoto] = useState<string | null>(null);
  const [erroRemoto, setErroRemoto] = useState<string | null>(null);
  const [carregando, setCarregando] = useState(false);
  const [identLinha, setIdentLinha] = useState<string | null>(null);
  const [forceRefresh, setForceRefresh] = useState(0);

  useEffect(() => {
    if (!leiMeta || !leiId) return;

    // Dados de teste para demonstração
    if (leiId === 'test-cdc') {
      setCarregando(true);
      setTimeout(() => {
        const testHtml = `
          <div class="texto">
            <p class="ident-lei"><strong>LEI Nº 8.078, DE 11 DE SETEMBRO DE 1990</strong></p>
            <p class="ident-desc"><em>Dispõe sobre a proteção do consumidor e dá outras providências.</em></p>

            <p class="heading heading-livro">LIVRO I - DOS DIREITOS DO CONSUMIDOR</p>

            <p class="heading heading-titulo">TÍTULO I - DOS DIREITOS DO CONSUMIDOR</p>

            <p class="heading heading-capitulo">CAPÍTULO I - DISPOSIÇÕES GERAIS</p>

            <div id="art-1">
              <strong>Art. 1º</strong> O presente código estabelece normas de proteção e defesa do consumidor, de ordem pública e interesse social, nos termos dos arts. 5º, inciso XXXII, 170, inciso V, da Constituição Federal e art. 48 de suas Disposições Transitórias.
            </div>

            <div id="art-2">
              <strong>Art. 2º</strong> Consumidor é toda pessoa física ou jurídica que adquire ou utiliza produto ou serviço como destinatário final.
            </div>

            <div id="art-3">
              <strong>Art. 3º</strong> Fornecedor é toda pessoa física ou jurídica, pública ou privada, nacional ou estrangeira, bem como os entes despersonalizados, que desenvolvem atividade de produção, montagem, criação, construção, transformação, importação, exportação, distribuição ou comercialização de produtos ou prestação de serviços.
            </div>

            <p class="heading heading-capitulo">CAPÍTULO II - DA POLÍTICA NACIONAL DE RELAÇÕES DE CONSUMO</p>

            <div id="art-4">
              <strong>Art. 4º</strong> A Política Nacional das Relações de Consumo tem por objetivo o atendimento das necessidades dos consumidores, o respeito à sua dignidade, saúde e segurança, a proteção de seus interesses econômicos, a melhoria da sua qualidade de vida, bem como a transparência e harmonia das relações de consumo.
            </div>

            <div id="art-5">
              <strong>Art. 5º</strong> Para a execução da Política Nacional das Relações de Consumo, contará o poder público com os seguintes instrumentos, entre outros.
            </div>

            <p class="heading heading-capitulo">CAPÍTULO III - DOS DIREITOS BÁSICOS DO CONSUMIDOR</p>

            <div id="art-6">
              <strong>Art. 6º</strong> São direitos básicos do consumidor.
            </div>

            <div id="art-7">
              <strong>Art. 7º</strong> Os direitos previstos neste código não excluem outros decorrentes de tratados ou convenções internacionais de que o Brasil seja signatário, da legislação interna ordinária, de regulamentos expedidos pelas autoridades administrativas competentes, bem como dos que derivem dos princípios gerais do direito, analogia, costumes e eqüidade.
            </div>

            <p class="heading heading-titulo">TÍTULO II - DAS INFRAÇÕES PENAIS</p>

            <p class="heading heading-capitulo">CAPÍTULO I - DOS CRIMES CONTRA AS RELAÇÕES DE CONSUMO</p>

            <p class="heading heading-secao">SEÇÃO I - DOS CRIMES CONTRA A ECONOMIA POPULAR</p>

            <div id="art-61">
              <strong>Art. 61.</strong> Constituem crimes contra as relações de consumo previstas neste código, sem prejuízo do disposto no Código Penal e leis especiais, as condutas tipificadas nos artigos seguintes.
            </div>

            <div id="art-62">
              <strong>Art. 62.</strong> (Vetado).
            </div>

            <p class="heading heading-secao">SEÇÃO II - DOS CRIMES CONTRA A SAÚDE PÚBLICA</p>

            <div id="art-63">
              <strong>Art. 63.</strong> Omitir dizeres ou sinais ostensivos sobre a nocividade ou periculosidade de produtos, nas embalagens, nos invólucros, recipientes ou publicidade.
            </div>

            <p class="subsecao">SUBSEÇÃO I - DAS DISPOSIÇÕES ESPECIAIS</p>

            <div id="art-64">
              <strong>Art. 64.</strong> Deixar de comunicar à autoridade competente e aos consumidores a nocividade ou periculosidade de produtos cujo conhecimento seja posterior à sua colocação no mercado.
            </div>
          </div>
        `;

        setHtmlRemoto(testHtml);
        setIdentLinha('LEI Nº 8.078, DE 11 DE SETEMBRO DE 1990');
        setCarregando(false);
      }, 1000);
      return;
    }

    if (!leiMeta?.url) return;

    setHtmlRemoto(null);
    setErroRemoto(null);
    setCarregando(true);

    const controller = new AbortController();

    // Clear cache if this is a forced refresh
    if (forceRefresh > 0) {
      const CACHE_PREFIX = "lawCache_";
      const cacheKey = CACHE_PREFIX + encodeURIComponent(leiMeta.url);
      if (typeof window !== "undefined") {
        localStorage.removeItem(cacheKey);
      }
    }

    fetchLawHtml(leiId, leiMeta.url, controller.signal)
      .then(({ html, source }) => {
        console.debug(`[useRemoteLaw] Successfully loaded ${leiId} from ${source}`);

        // Check if html is valid
        if (!html || typeof html !== 'string') {
          throw new Error(`Conteúdo da lei ${leiMeta.nome} não foi encontrado. Tente novamente mais tarde.`);
        }

        // Check if we got placeholder content
        if (html.includes('indisponível offline') || html.length < 1000) {
          throw new Error(`Conteúdo da lei ${leiMeta.nome} não está disponível. Tente novamente mais tarde.`);
        }

        // limpeza + formatação
        let sanitized = html
          .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, "")
          .replace(/<link[^>]*rel=["']?stylesheet["']?[^>]*>/gi, "")
          .replace(/<img[^>]*brastra\.gif[^>]*>/gi, "")
          .replace(/&nbsp;/gi, " ")
          .replace(/(<br\s*\/?>\s*){2,}/gi, "<br/>")
          .replace(/color\s*:\s*[^;"']+/gi, "")
          .replace(/background(?:-color)?\s*:\s*[^;"']+/gi, "")
          .replace(/font-size\s*:\s*[^;"']+/gi, "")
          .replace(/<font[^>]*color="[^"]*"[^>]*>([\s\S]*?)<\/font>/gi, "$1")
          .replace(/<font[^>]*size="[^"]*"[^>]*>([\s\S]*?)<\/font>/gi, "$1");

        sanitized = sanitized
          .replace(/<div class="doc-header">[\s\S]*?<\/div>/gi, "")
          .replace(
            /<center>[\s\S]*?(?:Presidência da República|Casa Civil da Presidência|Secretaria-Geral da Presidência|Subchefia para Assuntos Jurídicos)[\s\S]*?<\/center>/gi,
            ""
          )
          .replace(
            /<p[^>]*>[^<]*(?:Presidência da República|Casa Civil|Secretaria-Geral|Subchefia para Assuntos Jurídicos)[\s\S]*?<\/p>/gi,
            ""
          );

        sanitized = improveFormatting(sanitized, leiMeta);

        const idMatch = sanitized.match(
          /<p class='ident-lei'><strong>([^<]+)<\/strong><\/p>/i
        );
        setIdentLinha(idMatch ? idMatch[1] : null);

        setHtmlRemoto(sanitized);
      })
      .catch((err: any) => {
        // Handle AbortError gracefully - this is expected when component unmounts or effect re-runs
        if (err.name === "AbortError" || err.message?.includes("aborted")) {
          // Only log in development mode and only as debug
          if (import.meta.env.DEV) {
            console.debug(`[useRemoteLaw] Request aborted for ${leiId} (this is normal in React StrictMode)`);
          }
          return;
        }

        console.error(`[useRemoteLaw] Error loading ${leiId}:`, err);

        // Provide user-friendly error messages
        let userMessage = "Erro ao carregar a lei. Verifique sua conexão com a internet e tente novamente.";

        if (err.message) {
          if (err.message.includes('não está disponível')) {
            userMessage = err.message;
          } else if (err.message.includes('Invalid')) {
            userMessage = "O conteúdo da lei não pôde ser carregado corretamente. Tente novamente em alguns minutos.";
          } else if (err.message.includes('timeout') || err.message.includes('network')) {
            userMessage = "Tempo limite excedido. Verifique sua conexão e tente novamente.";
          } else if (err.message.includes('QuotaExceededError') || err.message.includes('quota')) {
            userMessage = "Espaço de armazenamento insuficiente. Limpe o cache do navegador e tente novamente.";
          } else if (err.message.includes('HTTP 404') || err.message.includes('Cannot GET')) {
            userMessage = "Servidor de leis não está disponível. Verifique se o backend está rodando na porta 3005.";
          } else if (err.message.includes('Failed to fetch') || err.message.includes('NetworkError')) {
            userMessage = "Erro de conexão. Verifique sua internet e se o servidor está rodando.";
          }
        }

        setErroRemoto(userMessage);
      })
      .finally(() => {
        setCarregando(false);
      });

    return () => controller.abort();
  }, [leiMeta?.url, leiId, leiMeta, forceRefresh]);

  const refreshLaw = () => {
    // Check if last refresh was within 24 hours
    const lastRefreshKey = `lastRefresh_${leiId}`;
    const lastRefresh = localStorage.getItem(lastRefreshKey);
    const now = Date.now();

    if (lastRefresh && (now - parseInt(lastRefresh)) < 24 * 60 * 60 * 1000) {
      const hoursLeft = Math.ceil((24 * 60 * 60 * 1000 - (now - parseInt(lastRefresh))) / (60 * 60 * 1000));
      throw new Error(`Aguarde ${hoursLeft}h para atualizar novamente`);
    }

    localStorage.setItem(lastRefreshKey, now.toString());
    setForceRefresh(prev => prev + 1);
  };

  return { htmlRemoto, erroRemoto, carregando, identLinha, refreshLaw };
}
