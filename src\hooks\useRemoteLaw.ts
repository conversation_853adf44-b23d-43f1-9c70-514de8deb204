import { useState, useEffect } from 'react';
import { LeiMeta } from '../data/leis';
import { fetchLawHtml, improveFormatting } from '../utils';

export function useRemoteLaw(leiId: string | undefined, leiMeta: LeiMeta | undefined) {
  const [htmlRemoto, setHtmlRemoto] = useState<string | null>(null);
  const [erroRemoto, setErroRemoto] = useState<string | null>(null);
  const [carregando, setCarregando] = useState(false);
  const [identLinha, setIdentLinha] = useState<string | null>(null);
  const [forceRefresh, setForceRefresh] = useState(0);

  useEffect(() => {
    if (!leiMeta?.url || !leiId) return;

    setHtmlRemoto(null);
    setErroRemoto(null);
    setCarregando(true);

    const controller = new AbortController();

    // Clear cache if this is a forced refresh
    if (forceRefresh > 0) {
      const CACHE_PREFIX = "lawCache_";
      const cacheKey = CACHE_PREFIX + encodeURIComponent(leiMeta.url);
      if (typeof window !== "undefined") {
        localStorage.removeItem(cacheKey);
      }
    }

    fetchLawHtml(leiId, leiMeta.url, controller.signal)
      .then(({ html, source }) => {
        console.debug(`[useRemoteLaw] Successfully loaded ${leiId} from ${source}`);

        // Check if html is valid
        if (!html || typeof html !== 'string') {
          throw new Error(`Conteúdo da lei ${leiMeta.nome} não foi encontrado. Tente novamente mais tarde.`);
        }

        // Check if we got placeholder content
        if (html.includes('indisponível offline') || html.length < 1000) {
          throw new Error(`Conteúdo da lei ${leiMeta.nome} não está disponível. Tente novamente mais tarde.`);
        }

        // limpeza + formatação
        let sanitized = html
          .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, "")
          .replace(/<link[^>]*rel=["']?stylesheet["']?[^>]*>/gi, "")
          .replace(/<img[^>]*brastra\.gif[^>]*>/gi, "")
          .replace(/&nbsp;/gi, " ")
          .replace(/(<br\s*\/?>\s*){2,}/gi, "<br/>")
          .replace(/color\s*:\s*[^;"']+/gi, "")
          .replace(/background(?:-color)?\s*:\s*[^;"']+/gi, "")
          .replace(/font-size\s*:\s*[^;"']+/gi, "")
          .replace(/<font[^>]*color="[^"]*"[^>]*>([\s\S]*?)<\/font>/gi, "$1")
          .replace(/<font[^>]*size="[^"]*"[^>]*>([\s\S]*?)<\/font>/gi, "$1");

        sanitized = sanitized
          .replace(/<div class="doc-header">[\s\S]*?<\/div>/gi, "")
          .replace(
            /<center>[\s\S]*?(?:Presidência da República|Casa Civil da Presidência|Secretaria-Geral da Presidência|Subchefia para Assuntos Jurídicos)[\s\S]*?<\/center>/gi,
            ""
          )
          .replace(
            /<p[^>]*>[^<]*(?:Presidência da República|Casa Civil|Secretaria-Geral|Subchefia para Assuntos Jurídicos)[\s\S]*?<\/p>/gi,
            ""
          );

        sanitized = improveFormatting(sanitized, leiMeta);

        const idMatch = sanitized.match(
          /<p class='ident-lei'><strong>([^<]+)<\/strong><\/p>/i
        );
        setIdentLinha(idMatch ? idMatch[1] : null);

        setHtmlRemoto(sanitized);
      })
      .catch((err: any) => {
        // Handle AbortError gracefully - this is expected when component unmounts or effect re-runs
        if (err.name === "AbortError" || err.message?.includes("aborted")) {
          // Only log in development mode and only as debug
          if (import.meta.env.DEV) {
            console.debug(`[useRemoteLaw] Request aborted for ${leiId} (this is normal in React StrictMode)`);
          }
          return;
        }

        console.error(`[useRemoteLaw] Error loading ${leiId}:`, err);

        // Provide user-friendly error messages
        let userMessage = "Erro ao carregar a lei. Verifique sua conexão com a internet e tente novamente.";

        if (err.message) {
          if (err.message.includes('não está disponível')) {
            userMessage = err.message;
          } else if (err.message.includes('Invalid')) {
            userMessage = "O conteúdo da lei não pôde ser carregado corretamente. Tente novamente em alguns minutos.";
          } else if (err.message.includes('timeout') || err.message.includes('network')) {
            userMessage = "Tempo limite excedido. Verifique sua conexão e tente novamente.";
          } else if (err.message.includes('QuotaExceededError') || err.message.includes('quota')) {
            userMessage = "Espaço de armazenamento insuficiente. Limpe o cache do navegador e tente novamente.";
          } else if (err.message.includes('HTTP 404') || err.message.includes('Cannot GET')) {
            userMessage = "Servidor de leis não está disponível. Verifique se o backend está rodando na porta 3005.";
          } else if (err.message.includes('Failed to fetch') || err.message.includes('NetworkError')) {
            userMessage = "Erro de conexão. Verifique sua internet e se o servidor está rodando.";
          }
        }

        setErroRemoto(userMessage);
      })
      .finally(() => {
        setCarregando(false);
      });

    return () => controller.abort();
  }, [leiMeta?.url, leiId, leiMeta, forceRefresh]);

  const refreshLaw = () => {
    // Check if last refresh was within 24 hours
    const lastRefreshKey = `lastRefresh_${leiId}`;
    const lastRefresh = localStorage.getItem(lastRefreshKey);
    const now = Date.now();

    if (lastRefresh && (now - parseInt(lastRefresh)) < 24 * 60 * 60 * 1000) {
      const hoursLeft = Math.ceil((24 * 60 * 60 * 1000 - (now - parseInt(lastRefresh))) / (60 * 60 * 1000));
      throw new Error(`Aguarde ${hoursLeft}h para atualizar novamente`);
    }

    localStorage.setItem(lastRefreshKey, now.toString());
    setForceRefresh(prev => prev + 1);
  };

  return { htmlRemoto, erroRemoto, carregando, identLinha, refreshLaw };
}
