# Firebase Configuration
VITE_FIREBASE_API_KEY=your_firebase_api_key_here
VITE_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your_project_id
VITE_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
VITE_FIREBASE_APP_ID=your_app_id

# AI Service API Keys (Optional)
VITE_OPENAI_API_KEY=sk-your_openai_key_here
VITE_GEMINI_API_KEY=your_gemini_key_here

# Backend URLs
VITE_BACKEND_URL=http://localhost:3005
VITE_STRIPE_BACKEND_URL=http://localhost:4242

# Stripe Configuration
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here

# Cache Configuration
VITE_LAW_CACHE_MAX_DAYS=30

# Gemini AI Configuration (Optional)
VITE_GEMINI_MODEL=gemini-2.0-flash
VITE_GEMINI_API_VERSION=v1

# Memory Monitoring Configuration
# Set to 'true' to enable memory monitoring for debugging/optimization
# Default: 'false' for better performance in production
VITE_ENABLE_MEMORY_MONITORING=false
