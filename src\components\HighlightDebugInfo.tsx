import React, { useState } from 'react';
import { Bug } from 'lucide-react';

interface Props {
  highlights: any[];
  isLoading: boolean;
  isAuthenticated: boolean;
  leiId: string;
}

export function HighlightDebugInfo({ highlights, isLoading, isAuthenticated, leiId }: Props) {
  const [showDebug, setShowDebug] = useState(false);

  if (process.env.NODE_ENV === 'production') {
    return null;
  }

  const guestMode = typeof localStorage !== 'undefined' && localStorage.getItem('guest_mode') === '1';
  const uid = isAuthenticated ? 'authenticated' : 'guest';
  const storageKey = `grifos-${uid}-${leiId}`;
  
  let localStorageData = null;
  try {
    const raw = localStorage.getItem(storageKey);
    localStorageData = raw ? JSON.parse(raw) : null;
  } catch (e) {
    localStorageData = 'Error parsing localStorage data';
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <button
        onClick={() => setShowDebug(!showDebug)}
        className="p-2 bg-gray-800 text-white rounded-full shadow-lg hover:bg-gray-700"
        title="Toggle highlight debug info"
      >
        <Bug size={16} />
      </button>
      
      {showDebug && (
        <div className="absolute bottom-12 right-0 bg-black text-white p-4 rounded-lg shadow-xl max-w-md text-xs">
          <h3 className="font-bold mb-2">Highlight Debug Info</h3>
          <div className="space-y-1">
            <div><strong>Lei ID:</strong> {leiId}</div>
            <div><strong>Loading:</strong> {isLoading ? 'Yes' : 'No'}</div>
            <div><strong>Authenticated:</strong> {isAuthenticated ? 'Yes' : 'No'}</div>
            <div><strong>Guest Mode:</strong> {guestMode ? 'Yes' : 'No'}</div>
            <div><strong>Storage Key:</strong> {storageKey}</div>
            <div><strong>Highlights Count:</strong> {highlights.length}</div>
            <div><strong>Firebase Available:</strong> {typeof window !== 'undefined' && (window as any).firebase ? 'Yes' : 'No'}</div>
            
            <details className="mt-2">
              <summary className="cursor-pointer font-semibold">Highlights Data</summary>
              <pre className="mt-1 text-xs overflow-auto max-h-32">
                {JSON.stringify(highlights, null, 2)}
              </pre>
            </details>
            
            <details className="mt-2">
              <summary className="cursor-pointer font-semibold">localStorage Data</summary>
              <pre className="mt-1 text-xs overflow-auto max-h-32">
                {JSON.stringify(localStorageData, null, 2)}
              </pre>
            </details>
          </div>
        </div>
      )}
    </div>
  );
}
