import type { NavigateFunction } from 'react-router-dom';
import type { LeiMeta } from '../data/leis';
import type { Highlight } from '../hooks/useHighlights';
import type { Article as ArticleType } from '../types';
import type { TocItem } from '../hooks/useTOC';
import type { VirtualLeiHandle } from '../components/VirtualLei';

// Base interface for common properties
export interface BaseLawViewProps {
  containerRef?: React.RefObject<HTMLDivElement>;
  leiMeta: LeiMeta;
  navigate: NavigateFunction;
}

// Interface for highlight-related functionality
export interface HighlightProps {
  corAtiva: string;
  setCorAtiva: (color: string) => void;
  highlights: Highlight[];
  removeHighlight: (id: string) => Promise<void>;
  exportarGrifos: () => void;
  importarGrifos: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

// Interface for font size controls
export interface FontControlProps {
  diminuir: () => void;
  aumentar: () => void;
}

// Interface for content display
export interface ContentProps {
  virtualRef: React.RefObject<VirtualLeiHandle>;
  visibleArtigos: ArticleType[];
  handleScroll: (e: React.UIEvent<HTMLDivElement>) => void;
  tocItems: TocItem[];
}

// Interface for paywall functionality
export interface PaywallProps {
  showPaywall: boolean;
  handlePaywallDismiss: () => void;
  creditsLeft: number;
}

// Interface for AI dialog functionality
export interface AIDialogProps {
  dialogArt: any;
  closeDialog: () => void;
  loadingExp: boolean;
  handleExplain: () => void;
  handleExplainWithExample: () => void;
  loadingJur: boolean;
  handleJurisprudence: () => void;
  dialogExp: string | null;
  dialogJur: string | null;
  formatIaText: (text: string) => string;
}

// Combined interface for LocalLawView
export interface LocalLawViewProps extends 
  BaseLawViewProps,
  HighlightProps,
  FontControlProps,
  ContentProps,
  PaywallProps,
  AIDialogProps {}

// Specific interfaces for sub-components
export interface LawHeaderProps {
  leiMeta: LeiMeta;
  navigate: NavigateFunction;
  corAtiva: string;
  setCorAtiva: (color: string) => void;
  diminuir: () => void;
  aumentar: () => void;
  exportarGrifos: () => void;
  importarGrifos: (e: React.ChangeEvent<HTMLInputElement>) => void;
  toggleHierarchicalNav: () => void;
  onProfileClick: () => void;
  isHierarchicalNavVisible?: boolean;
  hasHierarchicalContext?: boolean;
}

export interface PaywallModalProps {
  isVisible: boolean;
  creditsLeft: number;
  navigate: NavigateFunction;
  onDismiss: () => void;
}

export interface ArticleModalProps {
  dialogArt: any;
  onClose: () => void;
  loadingExp: boolean;
  loadingJur: boolean;
  onExplain: () => void;
  onExplainWithExample: () => void;
  onJurisprudence: () => void;
  dialogExp: string | null;
  dialogJur: string | null;
  formatIaText: (text: string) => string;
}
