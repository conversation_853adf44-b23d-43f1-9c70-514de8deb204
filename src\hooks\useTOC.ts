import { useState, useEffect } from 'react';

export interface TocItem {
  id: string;
  title: string;
}

/**
 * Hook para extrair um Sumário (Table of Contents) de um HTML de lei.
 * @param htmlRemoto - O conteúdo HTML da lei, como uma string.
 * @returns Um array de itens do sumário.
 */
export function useTOC(htmlRemoto: string | null) {
  const [tocItems, setTocItems] = useState<TocItem[]>([]);

  useEffect(() => {
    if (!htmlRemoto) {
      setTocItems([]);
      return;
    }

    // Usamos um div temporário para parsear o HTML sem renderizá-lo no DOM principal
    const temp = document.createElement('div');
    temp.innerHTML = htmlRemoto;

    const arts = Array.from(temp.querySelectorAll('[id^="art-"]')) as HTMLElement[];
    const seen = new Set<string>();
    const items: TocItem[] = [];

    arts.forEach((el) => {
      const id = el.id;
      if (seen.has(id)) return; // Evita duplicados de artigos com mesmo ID
      seen.add(id);

      const num = id.replace('art-', '');
      // Tenta encontrar o texto do 'strong' para o título, ou usa o número do artigo como fallback
      const title = el.querySelector('strong')?.textContent || `Art. ${num}`;
      items.push({ id, title });
    });

    setTocItems(items);

  }, [htmlRemoto]); // Re-executa apenas quando o HTML da lei muda

  return { tocItems };
}
