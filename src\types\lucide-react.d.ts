declare module "lucide-react" {
  import * as React from "react";
  export interface LucideProps extends React.SVGProps<SVGSVGElement> {
    color?: string;
    size?: string | number;
  }
  export const Sun: React.FC<LucideProps>;
  export const Moon: React.FC<LucideProps>;
  export const Search: React.FC<LucideProps>;
  export const ChevronLeft: React.FC<LucideProps>;
  export const Highlighter: React.FC<LucideProps>;
  export const Info: React.FC<LucideProps>;
  export const MoreVertical: React.FC<LucideProps>;
  // Adicione outros ícones conforme necessário
} 