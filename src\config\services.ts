import { 
  container, 
  ServiceKeys, 
  setupDefaultServices,
  ConsoleLogger,
  MemoryCacheManager 
} from '../abstractions/ServiceContainer';
import { CompositeHighlightRenderer } from '../abstractions/HighlightRenderer';

/**
 * Initialize all services for the application
 * This follows the Dependency Inversion Principle by setting up
 * all dependencies at the application root
 */
export function initializeServices(): void {
  // Setup default services first
  setupDefaultServices();

  // Register highlight renderer
  container.registerSingleton(
    ServiceKeys.HIGHLIGHT_RENDERER, 
    () => new CompositeHighlightRenderer()
  );

  // You can override default services here if needed
  // For example, in production you might want a different logger:
  // container.registerSingleton(ServiceKeys.LOGGER, () => new ProductionLogger());

  console.log('✅ Services initialized successfully');
}

/**
 * Get all registered services (useful for debugging)
 */
export function getRegisteredServices(): string[] {
  return Object.values(ServiceKeys);
}

/**
 * Health check for all services
 */
export function healthCheckServices(): { [key: string]: boolean } {
  const results: { [key: string]: boolean } = {};
  
  for (const serviceKey of Object.values(ServiceKeys)) {
    try {
      const service = container.resolve(serviceKey);
      results[serviceKey] = !!service;
    } catch (error) {
      results[serviceKey] = false;
      console.error(`Health check failed for service ${serviceKey}:`, error);
    }
  }
  
  return results;
}
