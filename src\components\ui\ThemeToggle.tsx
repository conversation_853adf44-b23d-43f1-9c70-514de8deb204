import { Sun, Moon, Palette, <PERSON>s, Trees, Sunset } from "lucide-react";
import { useTheme } from "../ThemeProvider";

export function ThemeToggle() {
  const { theme, toggle } = useTheme();

  const getThemeIcon = () => {
    switch (theme) {
      case "light": return <Sun size={18} />;
      case "dark": return <Moon size={18} />;
      case "sepia": return <Palette size={18} />;
      case "ocean": return <Waves size={18} />;
      case "forest": return <Trees size={18} />;
      case "sunset": return <Sunset size={18} />;
      default: return <Moon size={18} />;
    }
  };

  const getThemeLabel = () => {
    switch (theme) {
      case "light": return "Tema Claro";
      case "dark": return "Tema Escuro";
      case "sepia": return "Tema Sépia";
      case "ocean": return "Tema Oceano";
      case "forest": return "Tema Floresta";
      case "sunset": return "Tema Pôr do Sol";
      default: return "Alternar tema";
    }
  };

  return (
    <button
      onClick={toggle}
      className="app-icon-btn"
      aria-label={`${getThemeLabel()} - Clique para alternar`}
      title={getThemeLabel()}
    >
      {getThemeIcon()}
    </button>
  );
}