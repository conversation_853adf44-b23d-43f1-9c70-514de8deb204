import type { Highlight } from '../hooks/useHighlights';

/**
 * Abstract interface for highlight rendering strategies
 * Follows Strategy pattern and Open/Closed Principle
 */
export interface IHighlightRenderer {
  render(highlight: Highlight, container: Element, onRemove?: (id: string) => void): boolean;
  canHandle(highlight: Highlight): boolean;
}

/**
 * Base class for highlight renderers
 */
export abstract class BaseHighlightRenderer implements IHighlightRenderer {
  protected debugLog(message: string, ...args: any[]) {
    if (import.meta.env.DEV) {
      console.debug(`[${this.constructor.name}] ${message}`, ...args);
    }
  }

  protected createMarkElement(highlight: Highlight, onRemove?: (id: string) => void): HTMLElement {
    const mark = document.createElement('mark');
    mark.style.backgroundColor = highlight.color;
    mark.dataset.grifo = highlight.id;
    mark.className = 'highlight-mark';
    mark.style.cursor = 'pointer';
    mark.title = 'Clique para remover grifo';

    if (onRemove) {
      this.addRemovalHandlers(mark, highlight.id, onRemove);
    }

    return mark;
  }

  private addRemovalHandlers(mark: HTMLElement, highlightId: string, onRemove: (id: string) => void) {
    const handleRemoveClick = (e: Event) => {
      e.preventDefault();
      e.stopPropagation();
      
      const textContent = mark.textContent || '';
      const textNode = document.createTextNode(textContent);
      mark.parentNode?.replaceChild(textNode, mark);
      onRemove(highlightId);
    };

    mark.addEventListener('click', handleRemoveClick);
    mark.addEventListener('touchend', (e) => {
      e.preventDefault();
      handleRemoveClick(e);
    });
  }

  abstract render(highlight: Highlight, container: Element, onRemove?: (id: string) => void): boolean;
  abstract canHandle(highlight: Highlight): boolean;
}

/**
 * Renderer for simple highlights (single text node)
 */
export class SimpleHighlightRenderer extends BaseHighlightRenderer {
  canHandle(highlight: Highlight): boolean {
    // Simple highlights typically have the same start and end container paths
    return JSON.stringify(highlight.startContainerPath) === JSON.stringify(highlight.endContainerPath);
  }

  render(highlight: Highlight, container: Element, onRemove?: (id: string) => void): boolean {
    try {
      const { getNodeFromPathWithFallback } = require('../utils/nodeUtils');
      const startNode = getNodeFromPathWithFallback(
        highlight.startContainerPath, 
        container, 
        highlight.text, 
        true
      );

      if (!startNode || startNode.nodeType !== Node.TEXT_NODE) {
        return false;
      }

      const textContent = startNode.textContent || '';
      let textIndex = textContent.indexOf(highlight.text);

      if (textIndex === -1) {
        // Try finding by keywords
        const words = highlight.text.split(' ').filter(w => w.length > 2);
        for (const word of words) {
          textIndex = textContent.indexOf(word);
          if (textIndex !== -1) break;
        }
      }

      if (textIndex === -1) return false;

      const range = document.createRange();
      const startOffset = textIndex;
      const endOffset = Math.min(textIndex + highlight.text.length, textContent.length);

      range.setStart(startNode, startOffset);
      range.setEnd(startNode, endOffset);

      if (range.collapsed || range.toString().trim().length === 0) {
        range.detach();
        return false;
      }

      const mark = this.createMarkElement(highlight, onRemove);
      
      try {
        range.surroundContents(mark);
        this.debugLog(`Simple highlight ${highlight.id} rendered successfully`);
        return true;
      } catch (error) {
        // Fallback method
        const contents = range.extractContents();
        mark.appendChild(contents);
        range.insertNode(mark);
        this.debugLog(`Simple highlight ${highlight.id} rendered with fallback method`);
        return true;
      }
    } catch (error) {
      this.debugLog(`Failed to render simple highlight ${highlight.id}:`, error);
      return false;
    }
  }
}

/**
 * Renderer for complex highlights (multiple text nodes)
 */
export class ComplexHighlightRenderer extends BaseHighlightRenderer {
  canHandle(highlight: Highlight): boolean {
    // Complex highlights have different start and end container paths
    return JSON.stringify(highlight.startContainerPath) !== JSON.stringify(highlight.endContainerPath);
  }

  render(highlight: Highlight, container: Element, onRemove?: (id: string) => void): boolean {
    try {
      const { getNodeFromPathWithFallback, hasOverlappingHighlight, applySafeHighlight } = require('../utils/nodeUtils');
      
      const startNode = getNodeFromPathWithFallback(
        highlight.startContainerPath, 
        container, 
        highlight.text, 
        true
      );
      const endNode = getNodeFromPathWithFallback(
        highlight.endContainerPath, 
        container, 
        highlight.text, 
        false
      );

      if (!startNode || !endNode || 
          startNode.nodeType !== Node.TEXT_NODE || 
          endNode.nodeType !== Node.TEXT_NODE) {
        return false;
      }

      const range = document.createRange();
      const startLen = startNode.textContent?.length ?? 0;
      const endLen = endNode.textContent?.length ?? 0;

      const safeStart = Math.max(0, Math.min(highlight.startOffset, startLen - 1));
      const safeEnd = Math.max(1, Math.min(highlight.endOffset, endLen));

      range.setStart(startNode, safeStart);
      range.setEnd(endNode, safeEnd);

      if (range.collapsed || range.toString().trim().length === 0) {
        range.detach();
        return false;
      }

      if (hasOverlappingHighlight(startNode, endNode, safeStart, safeEnd, container)) {
        this.debugLog(`Complex highlight ${highlight.id} overlaps with existing highlight`);
        return true; // Consider it successful to avoid retries
      }

      const mark = this.createMarkElement(highlight, onRemove);
      
      if (applySafeHighlight(range, mark)) {
        this.debugLog(`Complex highlight ${highlight.id} rendered successfully`);
        return true;
      }

      range.detach();
      return false;
    } catch (error) {
      this.debugLog(`Failed to render complex highlight ${highlight.id}:`, error);
      return false;
    }
  }
}

/**
 * Composite renderer that delegates to appropriate strategy
 * Follows Strategy pattern and Chain of Responsibility
 */
export class CompositeHighlightRenderer implements IHighlightRenderer {
  private renderers: IHighlightRenderer[] = [
    new SimpleHighlightRenderer(),
    new ComplexHighlightRenderer()
  ];

  canHandle(highlight: Highlight): boolean {
    return this.renderers.some(renderer => renderer.canHandle(highlight));
  }

  render(highlight: Highlight, container: Element, onRemove?: (id: string) => void): boolean {
    for (const renderer of this.renderers) {
      if (renderer.canHandle(highlight)) {
        return renderer.render(highlight, container, onRemove);
      }
    }
    return false;
  }

  addRenderer(renderer: IHighlightRenderer): void {
    this.renderers.push(renderer);
  }

  removeRenderer(rendererClass: new() => IHighlightRenderer): void {
    this.renderers = this.renderers.filter(r => !(r instanceof rendererClass));
  }
}
