export function garbleScore(str: string) {
  const count = (re: RegExp) => (str.match(re) || []).length;
  return count(/[ÃÂ]/g) + count(/ï¿½/g) + count(/\uFFFD/g) * 2;
}

// Tenta reconstruir string assumindo que cada charCode foi originalmente um byte Latin-1
export function fixMisDecoded(str: string) {
  const bytes = new Uint8Array(str.length);
  for (let i = 0; i < str.length; i++) bytes[i] = str.charCodeAt(i) & 0xff;
  const encs = ["windows-1252", "iso-8859-1"] as const;
  let best = str;
  let bestScore = garbleScore(str);
  for (const enc of encs) {
    try {
      const decoded = new TextDecoder(enc).decode(bytes);
      const sc = garbleScore(decoded);
      if (sc < bestScore) {
        bestScore = sc;
        best = decoded;
      }
    } catch {}
  }
  return best;
}



/**
 * Função otimizada para combinar títulos separados usando DOM
 */
export function combineTitlesInDOM(container: HTMLElement): void {
  // Primeiro, identificar e marcar títulos potenciais para evitar flash
  const allParagraphs = container.querySelectorAll('p');
  const potentialTitles: HTMLElement[] = [];

  allParagraphs.forEach(p => {
    const text = p.textContent?.trim() || '';
    // Detectar títulos principais e subseções
    if (/^(LIVRO|PARTE|T[ÍI]TULO|CAP[ÍI]TULO|SE[ÇC][ÃA]O|SUBSE[ÇC][ÃA]O)\s+/i.test(text) ||
        /^(Alegação|Da\s+\w+|Do\s+\w+|Das\s+\w+|Dos\s+\w+|Na\s+\w+|No\s+\w+)$/i.test(text) ||
        /^(Disposições?\s+\w+|Normas?\s+\w+|Regras?\s+\w+)$/i.test(text) ||
        /^[IVX]+\s*[-–]\s*\w+/i.test(text) || // Itens numerados romanos: I - texto, II - texto, etc.
        /^\d+\s*[-–]\s*\w+/i.test(text)) {   // Itens numerados: 1 - texto, 2 - texto, etc.
      p.classList.add('processing-title');
      potentialTitles.push(p as HTMLElement);
    }
  });
  // Cache dos padrões regex para melhor performance
  const patterns = {
    titlePattern: /^(LIVRO|PARTE|T[ÍI]TULO|CAP[ÍI]TULO|SE[ÇC][ÃA]O)\s+(PRIMEIRO|SEGUNDO|TERCEIRO|QUARTO|QUINTO|SEXTO|S[ÉE]TIMO|OITAVO|NONO|D[ÉE]CIMO|[IVX]+|\d+|GERAL|[ÚU]NICO)$/i,
    completeTitle: /^PARTE\s+GERAL$/i,
    titleWithDesc: /^(LIVRO|PARTE|T[ÍI]TULO|CAP[ÍI]TULO|SE[ÇC][ÃA]O)\s+([IVX]+|\d+|PRIMEIRO|SEGUNDO|TERCEIRO|QUARTO|QUINTO|SEXTO|S[ÉE]TIMO|OITAVO|NONO|D[ÉE]CIMO|GERAL|[ÚU]NICO)\s+[A-ZÁÉÍÓÚÂÊÎÔÛÀÈÌÒÙÃÕÇ]/i,
    longTitleStart: /^(LIVRO|PARTE|T[ÍI]TULO|CAP[ÍI]TULO|SE[ÇC][ÃA]O)\s+([IVX]+|\d+|PRIMEIRO|SEGUNDO|TERCEIRO|QUARTO|QUINTO|SEXTO|S[ÉE]TIMO|OITAVO|NONO|D[ÉE]CIMO|GERAL|[ÚU]NICO)\s+(DO|DA|DOS|DAS|DE|À|AO|E)\s*$/i,
    descPattern: /^[A-ZÁÉÍÓÚÂÊÎÔÛÀÈÌÒÙÃÕÇ]/,
    excludePattern: /^(LIVRO|PARTE|T[ÍI]TULO|CAP[ÍI]TULO|SE[ÇC][ÃA]O|Art\b|§)/i,
    numberPattern: /^\d+/,
    // Padrão para capturar apenas títulos principais que devem ser processados individualmente
    processedTitlePattern: /^(LIVRO|PARTE|T[ÍI]TULO|CAP[ÍI]TULO|SE[ÇC][ÃA]O|SUBSE[ÇC][ÃA]O)\s+/i
  };

  // Função helper para determinar o tipo de título
  const getTitleType = (text: string): string => {
    const upperText = text.toUpperCase();
    if (upperText.includes('LIVRO')) return 'title-livro';
    if (upperText.includes('TÍTULO') || upperText.includes('TITULO')) return 'title-titulo';
    if (upperText.includes('CAPÍTULO') || upperText.includes('CAPITULO')) return 'title-capitulo';
    if (upperText.includes('SEÇÃO') || upperText.includes('SECAO')) return 'title-secao';
    if (upperText.includes('SUBSEÇÃO') || upperText.includes('SUBSECAO')) return 'title-secao';
    if (upperText.includes('PARTE')) return 'title-parte';
    // Subseções menores (Alegação, Da/Do/Das/Dos + palavra)
    if (/^(ALEGAÇÃO|DA\s+\w+|DO\s+\w+|DAS\s+\w+|DOS\s+\w+|NA\s+\w+|NO\s+\w+|DISPOSIÇÕES?\s+\w+|NORMAS?\s+\w+|REGRAS?\s+\w+)/i.test(text)) return 'title-secao';
    // Itens numerados (I -, II -, 1 -, 2 -, etc.)
    if (/^[IVX]+\s*[-–]\s*\w+/i.test(text) || /^\d+\s*[-–]\s*\w+/i.test(text)) return 'title-secao';
    // Subseção específica (Subseção I, II, III, etc.)
    if (/^Subse[çc][ãa]o\s+[IVX]+$/i.test(text)) return 'title-secao';
    return 'title-default';
  };

  // Função helper para validar descrição - versão simplificada
  const isValidDesc = (text: string): boolean => {
    return /^[A-ZÁÉÍÓÚÂÊÎÔÛÀÈÌÒÙÃÕÇ]/.test(text) &&
           text.length >= 3 &&
           text.length <= 200 &&
           !text.includes('Art.') &&
           !text.includes('§');
  };

  const paragraphs = Array.from(container.querySelectorAll('p'));
  const toRemove: HTMLElement[] = [];

  // Processar combinações heading + texto seguinte
  for (let i = 0; i < paragraphs.length - 1; i++) {
    const current = paragraphs[i];
    const next = paragraphs[i + 1];

    // Pular se já foi processado ou marcado para remoção
    if (current.classList.contains('combined-title') || toRemove.includes(current) || toRemove.includes(next)) continue;

    const currentText = current.textContent?.trim() || '';
    const nextText = next.textContent?.trim() || '';

    // CONDIÇÃO SIMPLIFICADA: heading + qualquer texto que não seja outro heading
    const isCurrentHeading = current.classList.contains('heading');
    const isCurrentTitle = /^(LIVRO|PARTE|T[ÍI]TULO|CAP[ÍI]TULO|SE[ÇC][ÃA]O|SUBSE[ÇC][ÃA]O)\s+/i.test(currentText);
    // Padrão mais abrangente para subseções
    const isSubsection = /^(Alegação|Da\s+\w+|Do\s+\w+|Das\s+\w+|Dos\s+\w+|Na\s+\w+|No\s+\w+|Disposições?\s+\w+|Normas?\s+\w+|Regras?\s+\w+)$/i.test(currentText) ||
                        /^[IVX]+\s*[-–]\s*\w+/i.test(currentText) || // Itens numerados romanos
                        /^\d+\s*[-–]\s*\w+/i.test(currentText) ||   // Itens numerados
                        /^Subse[çc][ãa]o\s+[IVX]+$/i.test(currentText) || // Subseção I, II, III, etc.
                        (/^[A-ZÁÉÍÓÚÂÊÎÔÛÀÈÌÒÙÃÕÇ][a-záéíóúâêîôûàèìòùãõç]+(\s+[A-ZÁÉÍÓÚÂÊÎÔÛÀÈÌÒÙÃÕÇ][a-záéíóúâêîôûàèìòùãõç]+)*$/.test(currentText) &&
                        currentText.length < 30 && !currentText.includes('Art.'));
    const hasNextText = nextText && nextText.length > 2;
    const nextNotHeading = !next.classList.contains('heading');
    const nextNotArticle = !/^Art\.|^§|^\d+[º°]/.test(nextText);

    const shouldCombine = (isCurrentHeading && (isCurrentTitle || isSubsection)) && hasNextText && nextNotHeading && nextNotArticle;

    if (shouldCombine) {
      const combinedTitle = `${currentText.toUpperCase()} - ${nextText.toUpperCase()}`;
      const titleType = getTitleType(currentText);
      current.innerHTML = combinedTitle;
      current.className = `combined-title ${titleType}`;
      toRemove.push(next);
    }
  }

  // Remover elementos marcados para remoção em uma única operação
  toRemove.forEach(element => element.remove());

  // Remover classes temporárias de processamento
  potentialTitles.forEach(p => {
    p.classList.remove('processing-title');
  });

  // Processar títulos individuais que não foram combinados
  const remainingParagraphs = Array.from(container.querySelectorAll('p'));
  remainingParagraphs.forEach(p => {
    const text = p.textContent?.trim() || '';
    if (patterns.processedTitlePattern.test(text) &&
        !p.classList.contains('combined-title') &&
        (p.classList.contains('heading') || p.classList.contains('livro') ||
         p.classList.contains('titulo') || p.classList.contains('capitulo') || p.classList.contains('secao'))) {
      const titleType = getTitleType(text);
      p.classList.add('combined-title', titleType);
      p.innerHTML = text.toUpperCase();
    }
  });
}

export function improveFormatting(originalHtml: string, leiMeta?: import("../data/leis").LeiMeta): string {
  let html = originalHtml;

  // ================= 1. Sanitização de cabeçalhos (já removido cabeçalho institucional no caller) =================

  // ================= 2. Identificação principal (já transformada em StudyScreen) =================
  html = html.replace(/<p[^>]*>(\s*LEI[^<]{5,120})<\/p>/i, (_match, leiLine) => {
    const upper = leiLine.trim().toUpperCase();
    return `<p class='ident-lei'><strong>${upper}</strong></p>`;
  });

  html = html.replace(/<p[^>]*>([^<]*Código[^<]{5,120})<\/p>/i, (_match, desc) => {
    return `<p class='ident-desc'><em>${desc.trim()}</em></p>`;
  });

  // Remove tags <i> que restaram para evitar itálico indesejado
  html = html.replace(/<i[^>]*>([\s\S]*?)<\/i>/gi, "$1");

  // ================= 3. Pré-processamento otimizado (títulos são processados no DOM) =================

  // ================= 4. Parágrafo-a-parágrafo =================
  html = html.replace(/<p[^>]*>([\s\S]*?)<\/p>/gi, (m, inner) => {
    const plain = inner.replace(/<[^>]+>/g, "").replace(/&nbsp;/gi, " ").trim();

    // Cabeçalhos
    const headerRe = /^(LIVRO|T[ÍI]TULO|CAP[ÍI]TULO|SE[ÇC]ÃO|PARTE)/i;
    if (headerRe.test(plain) && plain.length < 120) {
      let className = 'heading';
      const type = plain.match(headerRe)?.[0].toUpperCase() || '';
      if (type.startsWith('LIVRO')) {
        className += ' heading-livro'; // text-red-500
      } else if (type.startsWith('T')) { // TÍTULO
        className += ' heading-titulo'; // text-sky-600
      } else if (type.startsWith('CAP')) { // CAPÍTULO
        className += ' heading-capitulo'; // text-teal-600
      } else if (type.startsWith('SE')) { // SEÇÃO
        className += ' heading-secao'; // text-amber-600
      }

      // Limpar quebras de linha e espaços extras no conteúdo do cabeçalho
      const cleanInner = inner
        .replace(/<br\s*\/?>/gi, ' ')  // Remover <br> tags
        .replace(/\s+/g, ' ')          // Normalizar espaços múltiplos
        .trim();

      return `<p class="${className}">${cleanInner}</p>`;
    }

    // Artigos
    if (/^Art\.?/i.test(plain)) {
      const numMatch = plain.match(/^Art\.?\s*(\d+(?:\.\d+)*[º°]?[A-Z]?)/i);
      const rawNum = numMatch ? numMatch[1] : "";
      const cleanNum = rawNum.replace(/[º°]/, "").toLowerCase().replace(/\./g, "-");
      const artId = rawNum ? `art-${cleanNum}` : "";
      const formatted = inner.replace(
        /^(\s*(?:<[^>]+>\s*)*)(Art\.?\s*\d+(?:\.\d+)*[º°]?)/i,
        (_full: string, prefix: string, artText: string) =>
          `${prefix}<strong class='art-heading' data-art='${cleanNum}'>${artText}</strong> <button class='art-head-btn' data-art='${cleanNum}' aria-label='Ações do artigo'></button>`
      );
      return `<p class="art" id="${artId}">${formatted}</p>`;
    }

    // Parágrafos
    if (/^§/.test(plain) || /^Parágrafo único/i.test(plain)) {
      const formatted = inner.replace(/^(§\s*\d+[º°]?|Parágrafo único)/i, (m2: string) => `<strong>${m2}</strong>`);
      return `<p class="para">${formatted}</p>`;
    }

    // Incisos
    if (/^[IVXLCDM]+\s*[-–.]/i.test(plain)) {
      const formatted = inner.replace(/^([IVXLCDM]+\s*[-–.])/i, (m2: string) => `<strong>${m2}</strong>`);
      return `<p class="inciso">${formatted}</p>`;
    }

    // Notas (Redação dada…, Incluído…, Vigência…) -> p.note
    if (/Redação dada|Inclu[iú]d[oa]|Vig[êe]ncia|Mensagem de veto|Regulamento|Vide|Revogad[oa]/i.test(plain)) {
      let content = inner;
      if (!/<a /i.test(inner)) {
        const url = leiMeta?.url || "#";
        content = `<a href='${url}' target='_blank' rel='noopener noreferrer'>${inner}</a>`;
      }
      content = content.replace(/Lei[^\.]{3,200}\./i, (m: string) => `<span class='lei-ref'>${m}</span>`);
      return `<p class="note">${content}</p>`;
    }

    return m; // default untouched
  });

  /* ===== Passo pós-processamento: notas inline e referências de artigo ===== */

  // 1. notas inline dentro de parênteses
  html = html.replace(/\((\s*(?:Reda[cç]ão\s+dada|Inclu[iú]d[oa]|Alterad[oa]|Renumerad[oa]|Revogad[oa])[^)]{5,120})\)/gi, (_m, inner) => {
    return `<span class="note-inline">(${inner})</span>`;
  });

  // 2. referências a Artigos (Art. 5º etc.) – escapamos começo de linha para evitar cabeçalhos
  html = html.replace(/([^>])art\.\s*((?:<[^>]+>*\s*)?)(\d+(?:\.\d+)*[º°]?[A-Z]?)(\.?)/g, (_m, prefix, interTags = "", num, period) => {
    const cleanNum = num.replace(/[º°]/, "").toLowerCase().replace(/\./g, "-");
    const dot = period || "";
    return `${prefix}<span class="art-ref" data-art="${cleanNum}">Art. ${interTags}${num}${dot}</span>`;
  });



  /* ===== 3. Corrigir hrefs relativos para domínio do Planalto ===== */

  // Base preferencial — tenta aproveitar a própria URL da lei para construir raízes corretas.
  // Isso garante que caminhos como "/_Ato2021-2024/..." sejam resolvidos dentro de "/ccivil_03".
  const baseUrl = (() => {
    if (leiMeta?.url) {
      const u = new URL(leiMeta.url);
      // Mantemos sempre o prefixo "/ccivil_03" que contém a maior parte dos atos legais.
      return `${u.protocol}//${u.host}/ccivil_03/`;
    }
    return "https://www.planalto.gov.br/ccivil_03/";
  })();

  html = html.replace(/<a\s+[^>]*>/gi, (raw) => {
    // extrai href
    const hrefMatch = raw.match(/href\s*=\s*(["']?)([^'"\s>]+)\1/i);
    if (!hrefMatch) return raw;
    let url = hrefMatch[2];

    // ignora anchors internas
    if (url.startsWith('#')) return raw;

    // Se o link já é absoluto, apenas o mantemos
    if (!/^https?:\/\//i.test(url)) {
      // Alguns documentos usam caminhos começando com "/_Ato"; precisamos forçar o prefixo "/ccivil_03".
      if (url.startsWith('/')) {
        // Usa o construtor URL para realizar o join garantindo a barra
        try {
          url = new URL(url, baseUrl).href;
        } catch {
          // Fallback simples: concatena assegurando a barra
          url = `${baseUrl.replace(/\/$/, '')}/${url.replace(/^\//, '')}`;
        }
      } else {
        try {
          url = new URL(url, baseUrl).href;
        } catch {/* ignora erros de URL mal-formada */}
      }
    }

    // ---- Garantir prefixo /ccivil_03/ ----
    try {
      const parsed = new URL(url);
      if (!/\/ccivil_03\//i.test(parsed.pathname)) {
        // Insere /ccivil_03 logo após host
        parsed.pathname = `/ccivil_03${parsed.pathname.startsWith('/') ? '' : '/'}${parsed.pathname.replace(/^\/*/, '')}`;
        url = parsed.href;
      }
    } catch {/* ignore */}

    // garante target/rel
    let updated = raw
      .replace(/href\s*=\s*(["']?)[^'"\s>]+\1/i, `href="${url}"`)
      .replace(/target=['"][^'"]*['"]/i, '')
      .replace(/rel=['"][^'"]*['"]/i, '');

    updated = updated.replace(/<a/i, '<a target="_blank" rel="noopener noreferrer"');
    return updated;
  });

  return html;
} 