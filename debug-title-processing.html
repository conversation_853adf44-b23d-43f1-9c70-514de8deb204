<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug - Processamento de Títulos</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a9e;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        
        /* Estilos dos títulos para teste */
        .combined-title {
            padding: 0.8em 1.2em !important;
            margin: 1.5em 0 !important;
            border-radius: 0.5em !important;
            font-weight: 700 !important;
            letter-spacing: 1px !important;
            color: white !important;
            text-align: center !important;
            line-height: 1.3 !important;
        }
        
        .title-livro {
            border-left-color: #ef4444 !important;
            background: rgba(127, 29, 29, 0.85) !important;
        }
        
        .title-titulo {
            border-left-color: #3b82f6 !important;
            background: rgba(30, 58, 138, 0.85) !important;
        }
        
        .title-capitulo {
            border-left-color: #10b981 !important;
            background: rgba(6, 78, 59, 0.85) !important;
        }
        
        .title-secao {
            border-left-color: #f59e0b !important;
            background: rgba(146, 64, 14, 0.85) !important;
        }
        
        .title-parte {
            border-left-color: #8b5cf6 !important;
            background: rgba(76, 29, 149, 0.85) !important;
        }
        
        .title-default {
            border-left-color: #6b7280 !important;
            background: rgba(55, 65, 81, 0.85) !important;
        }
    </style>
</head>
<body>
    <h1>Debug - Processamento de Títulos</h1>
    
    <div class="test-container">
        <h2>Teste da Função getTitleType</h2>
        <button onclick="testGetTitleType()">Testar getTitleType</button>
        <button onclick="testTitleDetection()">Testar Detecção de Títulos</button>
        <button onclick="simulateRealTitles()">Simular Títulos Reais</button>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>

    <div class="test-container">
        <h2>Títulos de Teste</h2>
        <div id="test-titles">
            <p class="heading">LIVRO PRIMEIRO</p>
            <p class="Cap">Das Pessoas</p>
            <p class="heading">TÍTULO I</p>
            <p class="Cap">Das Pessoas Naturais</p>
            <p class="heading">CAPÍTULO I</p>
            <p class="Cap">Da Personalidade e da Capacidade</p>
            <p class="heading">SEÇÃO I</p>
            <p class="Cap">Da Personalidade</p>
        </div>
        <button onclick="processTestTitles()">Processar Títulos de Teste</button>
    </div>

    <script>
        function showResult(message, type = 'info') {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.className = `result ${type}`;
            resultDiv.style.display = 'block';
        }

        // Função getTitleType copiada do código original
        function getTitleType(text) {
            const upperText = text.toUpperCase();
            if (upperText.includes('LIVRO')) return 'title-livro';
            if (upperText.includes('TÍTULO') || upperText.includes('TITULO')) return 'title-titulo';
            if (upperText.includes('CAPÍTULO') || upperText.includes('CAPITULO')) return 'title-capitulo';
            if (upperText.includes('SEÇÃO') || upperText.includes('SECAO')) return 'title-secao';
            if (upperText.includes('PARTE')) return 'title-parte';
            return 'title-default';
        }

        function testGetTitleType() {
            const testCases = [
                'LIVRO PRIMEIRO',
                'TÍTULO I',
                'CAPÍTULO I',
                'SEÇÃO I',
                'PARTE GERAL',
                'TÍTULO II DOS DIREITOS',
                'CAPÍTULO III DA CAPACIDADE',
                'SEÇÃO II DA PERSONALIDADE'
            ];
            
            let results = 'Teste da função getTitleType:\n\n';
            
            testCases.forEach(testCase => {
                const result = getTitleType(testCase);
                results += `"${testCase}" -> ${result}\n`;
            });
            
            showResult(results, 'info');
        }

        function testTitleDetection() {
            const patterns = {
                titlePattern: /^(LIVRO|PARTE|T[ÍI]TULO|CAP[ÍI]TULO|SE[ÇC][ÃA]O)\s+(PRIMEIRO|SEGUNDO|TERCEIRO|QUARTO|QUINTO|SEXTO|S[ÉE]TIMO|OITAVO|NONO|D[ÉE]CIMO|[IVX]+|\d+|GERAL|[ÚU]NICO)$/i,
                processedTitlePattern: /^(LIVRO|PARTE|T[ÍI]TULO|CAP[ÍI]TULO|SE[ÇC][ÃA]O)\s+/i
            };
            
            const testCases = [
                'LIVRO PRIMEIRO',
                'TÍTULO I',
                'CAPÍTULO I',
                'SEÇÃO I',
                'PARTE GERAL',
                'Das Pessoas',
                'Da Personalidade'
            ];
            
            let results = 'Teste de detecção de padrões:\n\n';
            
            testCases.forEach(testCase => {
                const titleMatch = patterns.titlePattern.test(testCase);
                const processedMatch = patterns.processedTitlePattern.test(testCase);
                results += `"${testCase}":\n`;
                results += `  titlePattern: ${titleMatch}\n`;
                results += `  processedTitlePattern: ${processedMatch}\n\n`;
            });
            
            showResult(results, 'info');
        }

        function simulateRealTitles() {
            // Simular o HTML real que vemos nas imagens
            const container = document.getElementById('test-titles');
            container.innerHTML = `
                <p class="heading">LIVRO PRIMEIRO</p>
                <p class="Cap">Das Pessoas</p>
                <p class="heading">TÍTULO I</p>
                <p class="Cap">Das Pessoas Naturais</p>
                <p class="heading">CAPÍTULO I</p>
                <p class="Cap">Da Personalidade e da Capacidade</p>
                <p class="heading">SEÇÃO I</p>
                <p class="Cap">Da Personalidade</p>
            `;
            
            showResult('Títulos reais simulados criados. Clique em "Processar Títulos de Teste" para aplicar as cores.', 'info');
        }

        function processTestTitles() {
            const container = document.getElementById('test-titles');
            const paragraphs = Array.from(container.querySelectorAll('p'));
            
            let results = 'Processamento dos títulos:\n\n';
            
            const patterns = {
                processedTitlePattern: /^(LIVRO|PARTE|T[ÍI]TULO|CAP[ÍI]TULO|SE[ÇC][ÃA]O)\s+/i,
                descPattern: /^[A-ZÁÉÍÓÚÂÊÎÔÛÀÈÌÒÙÃÕÇ]/
            };
            
            const toRemove = [];
            
            for (let i = 0; i < paragraphs.length - 1; i++) {
                const current = paragraphs[i];
                const next = paragraphs[i + 1];
                
                if (toRemove.includes(current)) continue;
                
                const currentText = current.textContent?.trim() || '';
                const nextText = next.textContent?.trim() || '';
                
                // Verificar se é um título que precisa ser combinado
                if (current.classList.contains('heading') &&
                    patterns.processedTitlePattern.test(currentText) &&
                    next.classList.contains('Cap') &&
                    nextText && nextText.length > 3) {
                    
                    const combinedTitle = `${currentText.toUpperCase()} - ${nextText.toUpperCase()}`;
                    const titleType = getTitleType(currentText);
                    
                    results += `Combinando: "${currentText}" + "${nextText}" -> ${titleType}\n`;
                    
                    current.innerHTML = combinedTitle;
                    current.className = `combined-title ${titleType}`;
                    toRemove.push(next);
                }
            }
            
            // Remover elementos marcados
            toRemove.forEach(el => el.remove());
            
            results += `\nElementos removidos: ${toRemove.length}\n`;
            results += 'Processamento concluído!';
            
            showResult(results, 'success');
        }
    </script>
</body>
</html>
