import { useState, useCallback, useEffect, RefObject } from 'react';
import type { Article as ArticleType } from '../types';

export function useArticlePopup(containerRef: RefObject<HTMLElement>, dependency: any) {
  const [popupArticle, setPopupArticle] = useState<ArticleType | null>(null);
  const [popupPosition, setPopupPosition] = useState({ top: 0, left: 0 });

  const handleArticleHover = useCallback((event: React.MouseEvent, article: ArticleType) => {
    const rect = (event.target as HTMLElement).getBoundingClientRect();
    setPopupPosition({
      top: rect.bottom + window.scrollY + 5,
      left: rect.left + window.scrollX - 150,
    });
    setPopupArticle(article);
  }, []);

  const closePopup = useCallback(() => {
    setPopupArticle(null);
  }, []);

  useEffect(() => {
    const container = containerRef.current;
    if (!container || !dependency) return;

    const handlePointerOver = (e: Event) => {
      const target = e.target as HTMLElement;
      const artRef = target.closest('.art-ref') as HTMLElement | null;

      if (artRef && container) {
        const articleId = artRef.dataset.artid;
        if (!articleId) return;

        const artEl = container.querySelector(`[id='${articleId}']`);
        if (!artEl) return;

        let combinedText = artEl.textContent || '';
        let sibling = artEl.nextElementSibling;
        while (sibling && !sibling.classList.contains('art')) {
            combinedText += `\n${sibling.textContent || ''}`;
            sibling = sibling.nextElementSibling;
        }

        const article: ArticleType = {
          id: articleId,
          texto: combinedText.trim(),
        };

        handleArticleHover(e as unknown as React.MouseEvent, article);
      }
    };

    const handlePointerOut = (e: Event) => {
        const relatedTarget = (e as PointerEvent).relatedTarget as Node | null;
        if (relatedTarget && (relatedTarget as HTMLElement).closest('.article-popup')) {
            return;
        }
        closePopup();
    };

    container.addEventListener('mouseover', handlePointerOver);
    container.addEventListener('mouseout', handlePointerOut);

    return () => {
      container.removeEventListener('mouseover', handlePointerOver);
      container.removeEventListener('mouseout', handlePointerOut);
    };
  }, [dependency, containerRef, handleArticleHover, closePopup]);


  return {
    popupArticle,
    popupPosition,
    closePopup,
  };
}
