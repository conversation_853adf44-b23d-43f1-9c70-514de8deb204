import { useEffect, useState, useRef } from "react";

interface HierarchyLevel {
  livro: string;
  titulo: string;
  capitulo: string;
  secao: string;
  subsecao: string;
}

interface DebugInfo {
  totalHeadings: number;
  visibleHeadings: number;
  currentScroll: number;
  detectedElements: string[];
}

/**
 * Retorna a hierarquia (Livro • Título • Capítulo • Seção...) correspondente
 * ao primeiro artigo atualmente visível no viewport.
 *
 * @param containerRef Elemento que contém o texto da lei
 * @param deps Opcional: valores adicionais que devem disparar nova varredura (ex: htmlRemoto)
 */
export function useLawContext(
  containerRef: React.RefObject<HTMLElement | null>,
  deps: React.DependencyList = []
) {
  const [context, setContext] = useState<string>("");
  const [debugInfo, setDebugInfo] = useState<DebugInfo>({ totalHeadings: 0, visibleHeadings: 0, currentScroll: 0, detectedElements: [] });
  const lastRef = useRef<HierarchyLevel>({ livro: "", titulo: "", capitulo: "", secao: "", subsecao: "" });

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    // Comprehensive selector for different law document formats
    const headingSelectors = [
      'p.heading',           // Standard heading paragraphs
      '.L, .T, .C, .S, .SS', // Class-based headings
      'h1, h2, h3, h4, h5, h6', // Standard HTML headings
      '[class*="livro"], [class*="titulo"], [class*="capitulo"], [class*="secao"]', // Class name patterns
      'p:has-text("LIVRO"), p:has-text("TÍTULO"), p:has-text("CAPÍTULO"), p:has-text("SEÇÃO")', // Text-based detection
      'strong:contains("LIVRO"), strong:contains("TÍTULO"), strong:contains("CAPÍTULO"), strong:contains("SEÇÃO")', // Bold text
      'p[style*="font-weight"], p[style*="bold"]', // Styled paragraphs
      '.center, .centered', // Centered text (often headings)
      'p[align="center"]'   // Center-aligned paragraphs
    ];

    const headings: HTMLElement[] = [];

    // Collect all potential heading elements
    headingSelectors.forEach(selector => {
      try {
        const elements = Array.from(container.querySelectorAll<HTMLElement>(selector));
        headings.push(...elements);
      } catch (e) {
        // Some selectors might not work in all browsers, continue with others
      }
    });

    // Also scan for text patterns in all paragraphs
    const allParagraphs = Array.from(container.querySelectorAll<HTMLElement>('p, div'));
    allParagraphs.forEach(p => {
      const text = p.textContent?.trim() || '';
      if (isHeadingText(text) && !headings.includes(p)) {
        headings.push(p);
      }
    });

    // Remove duplicates and sort by document order
    const uniqueHeadings = Array.from(new Set(headings)).sort((a, b) => {
      const position = a.compareDocumentPosition(b);
      return position & Node.DOCUMENT_POSITION_FOLLOWING ? -1 : 1;
    });

    if (!uniqueHeadings.length) return;

    // Helper function to identify heading text patterns
    function isHeadingText(text: string): boolean {
      const patterns = [
        /^(LIVRO|PARTE)\s+[IVX\d]+/i,
        /^T[ÍI]TULO\s+[IVX\d]+/i,
        /^CAP[ÍI]TULO\s+[IVX\d]+/i,
        /^SE[ÇC][ÃA]O\s+[IVX\d]+/i,
        /^SUBSE[ÇC][ÃA]O\s+[IVX\d]+/i,
        /^(LIVRO|PARTE|T[ÍI]TULO|CAP[ÍI]TULO|SE[ÇC][ÃA]O)\s*[-–—]\s*/i,
        /^\s*(LIVRO|PARTE|T[ÍI]TULO|CAP[ÍI]TULO|SE[ÇC][ÃA]O)\s+/i
      ];
      return patterns.some(pattern => pattern.test(text));
    }

    function compute() {
      const scrollTop = window.scrollY;
      const viewportTop = scrollTop + 100; // Offset for better detection

      let hierarchy = { ...lastRef.current };
      const detectedElements: string[] = [];
      let visibleCount = 0;

      for (const h of uniqueHeadings) {
        const rect = h.getBoundingClientRect();
        const elementTop = rect.top + scrollTop;

        // Only consider headings that are above the current viewport position
        if (elementTop > viewportTop) break;

        visibleCount++;
        let txt = h.textContent?.trim() || '';

        // Try to get more complete text from next sibling if it looks like a continuation
        const next = h.nextElementSibling as HTMLElement | null;
        if (next && shouldIncludeNextElement(next, txt)) {
          const nextTxt = next.textContent?.trim() || '';
          if (nextTxt && nextTxt.length < 120) {
            txt = `${txt} – ${nextTxt}`;
          }
        }

        detectedElements.push(`${h.tagName}.${h.className}: "${txt}"`);

        // Classify and store the heading
        const classification = classifyHeading(txt, h);
        if (classification) {
          updateHierarchy(hierarchy, classification, txt);
        }
      }

      // Build the context string
      const parts = [
        hierarchy.livro,
        hierarchy.titulo,
        hierarchy.capitulo,
        hierarchy.secao,
        hierarchy.subsecao
      ].filter(Boolean);

      lastRef.current = hierarchy;
      setContext(parts.join(" • "));

      // Update debug info
      setDebugInfo({
        totalHeadings: uniqueHeadings.length,
        visibleHeadings: visibleCount,
        currentScroll: scrollTop,
        detectedElements
      });
    }

    // Helper functions
    function shouldIncludeNextElement(next: HTMLElement, currentText: string): boolean {
      const nextText = next.textContent?.trim() || '';
      return (
        !next.classList.contains("heading") &&
        !/^Art\b/i.test(nextText) &&
        nextText.length < 120 &&
        nextText.length > 0 &&
        !/^\d/.test(nextText) &&
        !isHeadingText(nextText)
      );
    }

    function classifyHeading(text: string, element: HTMLElement): string | null {
      const textLower = text.toLowerCase();

      // Check class-based classification first
      if (element.classList.contains('L')) return 'livro';
      if (element.classList.contains('T')) return 'titulo';
      if (element.classList.contains('C')) return 'capitulo';
      if (element.classList.contains('S')) return 'secao';
      if (element.classList.contains('SS')) return 'subsecao';

      // Text-based classification
      if (/^(livro|parte)\b/i.test(text)) return 'livro';
      if (/^t[íi]tulo\b/i.test(text)) return 'titulo';
      if (/^cap[íi]tulo\b/i.test(text)) return 'capitulo';
      if (/^subse[çc][ãa]o\b/i.test(text)) return 'subsecao';
      if (/^se[çc][ãa]o\b/i.test(text)) return 'secao';

      return null;
    }

    function updateHierarchy(hierarchy: HierarchyLevel, type: string, text: string) {
      const cleanText = text.replace(/^(L\.|T\.|C\.|S\.|SS\.)\s*/, '');

      switch (type) {
        case 'livro':
          hierarchy.livro = `L. ${cleanText}`;
          hierarchy.titulo = hierarchy.capitulo = hierarchy.secao = hierarchy.subsecao = "";
          break;
        case 'titulo':
          hierarchy.titulo = `T. ${cleanText}`;
          hierarchy.capitulo = hierarchy.secao = hierarchy.subsecao = "";
          break;
        case 'capitulo':
          hierarchy.capitulo = `C. ${cleanText}`;
          hierarchy.secao = hierarchy.subsecao = "";
          break;
        case 'secao':
          hierarchy.secao = `S. ${cleanText}`;
          hierarchy.subsecao = "";
          break;
        case 'subsecao':
          hierarchy.subsecao = `SS. ${cleanText}`;
          break;
      }
    }

    // Optimized scroll handler using requestAnimationFrame to avoid excessive computations
    let ticking = false;
    function handleScroll() {
      if (!ticking) {
        ticking = true;
        requestAnimationFrame(() => {
          compute();
          ticking = false;
        });
      }
    }
    window.addEventListener("scroll", handleScroll, { passive: true });
    compute(); // Initial computation

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [containerRef, ...deps]);

  return { context, debugInfo };
}