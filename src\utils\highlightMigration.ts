import type { Highlight } from "../hooks/useHighlights";
import { getNodeFromPathWithAdvancedFallback } from "./nodeUtils";
import { enrichHighlight, generateLawVersion } from "./highlightPersistence";

/**
 * Sistema de migração e validação de highlights quando lei é atualizada
 */

// Debug logging utility
const debugLog = (message: string, ...args: any[]) => {
  if (import.meta.env.DEV) {
    console.debug(`[HighlightMigration] ${message}`, ...args);
  }
};

export interface MigrationResult {
  validHighlights: Highlight[];
  migratedHighlights: Highlight[];
  orphanedHighlights: Highlight[];
  stats: {
    total: number;
    valid: number;
    migrated: number;
    orphaned: number;
  };
}

/**
 * Valida se um highlight ainda é válido na nova versão da lei
 */
export function validateHighlight(
  highlight: Highlight,
  container: Element
): boolean {
  try {
    // Tenta encontrar o nó usando o sistema avançado de fallback
    const node = getNodeFromPathWithAdvancedFallback(highlight, container);
    
    if (!node || node.nodeType !== Node.TEXT_NODE) {
      return false;
    }

    // Verifica se o texto ainda está presente
    const content = node.textContent || '';
    return content.includes(highlight.text);
  } catch (error) {
    debugLog(`Erro ao validar highlight ${highlight.id}:`, error);
    return false;
  }
}

/**
 * Tenta migrar um highlight órfão para a nova estrutura da lei
 */
export function migrateHighlight(
  highlight: Highlight,
  container: Element,
  lawContent: string
): Highlight | null {
  try {
    // Usa o sistema avançado de fallback para encontrar o nó
    const node = getNodeFromPathWithAdvancedFallback(highlight, container);
    
    if (!node || node.nodeType !== Node.TEXT_NODE) {
      debugLog(`Não foi possível migrar highlight ${highlight.id}: nó não encontrado`);
      return null;
    }

    // Recalcula o path do nó encontrado
    const newPath = getElementPath(node, container);
    
    // Encontra a posição do texto no nó
    const content = node.textContent || '';
    const textIndex = content.indexOf(highlight.text);
    
    if (textIndex === -1) {
      debugLog(`Não foi possível migrar highlight ${highlight.id}: texto não encontrado`);
      return null;
    }

    // Cria um novo highlight com os dados atualizados
    const migratedHighlight: Omit<Highlight, 'id'> = {
      ...highlight,
      startContainerPath: newPath,
      endContainerPath: newPath,
      startOffset: textIndex,
      endOffset: textIndex + highlight.text.length,
      lastValidated: Date.now()
    };

    // Enriquece com novos dados de persistência
    const enrichedHighlight = enrichHighlight(
      migratedHighlight,
      node,
      node,
      container,
      lawContent
    );

    debugLog(`Highlight ${highlight.id} migrado com sucesso`);
    return { ...enrichedHighlight, id: highlight.id };
  } catch (error) {
    debugLog(`Erro ao migrar highlight ${highlight.id}:`, error);
    return null;
  }
}

/**
 * Função auxiliar para obter o path de um elemento
 */
function getElementPath(element: Node, root: Node): number[] {
  const path: number[] = [];
  let current = element;
  while (current && current !== root) {
    const parent = current.parentNode;
    if (!parent) break;
    const index = Array.from(parent.childNodes).indexOf(current as ChildNode);
    path.unshift(index);
    current = parent;
  }
  return path;
}

/**
 * Migra todos os highlights para uma nova versão da lei
 */
export function migrateHighlights(
  highlights: Highlight[],
  container: Element,
  lawContent: string,
  newLawVersion: string
): MigrationResult {
  debugLog(`Iniciando migração de ${highlights.length} highlights`);

  const validHighlights: Highlight[] = [];
  const migratedHighlights: Highlight[] = [];
  const orphanedHighlights: Highlight[] = [];

  for (const highlight of highlights) {
    // Verifica se o highlight já está na versão atual
    if (highlight.lawVersion === newLawVersion) {
      validHighlights.push(highlight);
      continue;
    }

    // Tenta validar o highlight na nova versão
    if (validateHighlight(highlight, container)) {
      // Highlight ainda é válido, apenas atualiza a versão
      const updatedHighlight = {
        ...highlight,
        lawVersion: newLawVersion,
        lastValidated: Date.now()
      };
      validHighlights.push(updatedHighlight);
      debugLog(`Highlight ${highlight.id} validado e atualizado`);
      continue;
    }

    // Tenta migrar o highlight
    const migratedHighlight = migrateHighlight(highlight, container, lawContent);
    
    if (migratedHighlight) {
      migratedHighlights.push({
        ...migratedHighlight,
        lawVersion: newLawVersion
      });
    } else {
      orphanedHighlights.push(highlight);
    }
  }

  const stats = {
    total: highlights.length,
    valid: validHighlights.length,
    migrated: migratedHighlights.length,
    orphaned: orphanedHighlights.length
  };

  debugLog('Migração concluída:', stats);

  return {
    validHighlights,
    migratedHighlights,
    orphanedHighlights,
    stats
  };
}

/**
 * Detecta se uma lei foi atualizada comparando versões
 */
export function detectLawUpdate(
  highlights: Highlight[],
  currentLawVersion: string
): boolean {
  if (highlights.length === 0) return false;

  // Verifica se algum highlight tem uma versão diferente da atual
  return highlights.some(highlight => 
    highlight.lawVersion && highlight.lawVersion !== currentLawVersion
  );
}

/**
 * Limpa highlights órfãos antigos (mais de 30 dias sem validação)
 */
export function cleanupOrphanedHighlights(
  highlights: Highlight[],
  maxAgeMs: number = 30 * 24 * 60 * 60 * 1000 // 30 dias
): Highlight[] {
  const now = Date.now();
  
  return highlights.filter(highlight => {
    if (!highlight.lastValidated) {
      // Se não tem lastValidated, usa createdAt ou mantém
      return !highlight.createdAt || (now - highlight.createdAt) < maxAgeMs;
    }
    
    return (now - highlight.lastValidated) < maxAgeMs;
  });
}

/**
 * Gera relatório de saúde dos highlights
 */
export function generateHighlightHealthReport(
  highlights: Highlight[],
  container: Element
): {
  total: number;
  valid: number;
  invalid: number;
  withoutVersion: number;
  oldValidation: number;
} {
  const now = Date.now();
  const oneWeekMs = 7 * 24 * 60 * 60 * 1000;
  
  let valid = 0;
  let invalid = 0;
  let withoutVersion = 0;
  let oldValidation = 0;

  for (const highlight of highlights) {
    if (!highlight.lawVersion) {
      withoutVersion++;
    }

    if (!highlight.lastValidated || (now - highlight.lastValidated) > oneWeekMs) {
      oldValidation++;
    }

    if (validateHighlight(highlight, container)) {
      valid++;
    } else {
      invalid++;
    }
  }

  return {
    total: highlights.length,
    valid,
    invalid,
    withoutVersion,
    oldValidation
  };
}
