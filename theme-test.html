<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Theme Test - Grifos <PERSON>gais IA</title>
    <style>
        body {
            font-family: Inter, sans-serif;
            margin: 20px;
            padding: 20px;
            transition: all 0.3s ease;
        }
        
        .theme-preview {
            border: 2px solid #ccc;
            border-radius: 8px;
            padding: 20px;
            margin: 10px 0;
            min-height: 100px;
        }
        
        .theme-name {
            font-weight: bold;
            font-size: 18px;
            margin-bottom: 10px;
        }
        
        .theme-description {
            margin-bottom: 15px;
            opacity: 0.8;
        }
        
        .color-sample {
            display: inline-block;
            width: 30px;
            height: 30px;
            border-radius: 4px;
            margin-right: 10px;
            border: 1px solid rgba(0,0,0,0.2);
        }
        
        /* Light Theme */
        .light-theme {
            background: #f9fafb;
            color: #111827;
        }
        
        /* Dark Theme */
        .dark-theme {
            background: #111827;
            color: #f9fafb;
        }
        
        /* Sepia Theme */
        .sepia-theme {
            background: #f4ecd8;
            color: #5b4636;
        }
        
        /* Ocean Theme */
        .ocean-theme {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            color: #e2e8f0;
        }
        
        /* Forest Theme */
        .forest-theme {
            background: linear-gradient(135deg, #14532d 0%, #166534 50%, #15803d 100%);
            color: #f0fdf4;
        }
        
        /* Sunset Theme */
        .sunset-theme {
            background: linear-gradient(135deg, #7c2d12 0%, #ea580c 50%, #fb923c 100%);
            color: #fef7ed;
        }
    </style>
</head>
<body>
    <h1>Grifos Legais IA - Theme Preview</h1>
    <p>This page shows previews of all 6 available themes in the application.</p>
    
    <div class="theme-preview light-theme">
        <div class="theme-name">1. Light Theme (Tema Claro)</div>
        <div class="theme-description">Clean and bright theme for daytime reading</div>
        <div class="color-sample" style="background: #f9fafb;"></div>
        <div class="color-sample" style="background: #111827;"></div>
        <div class="color-sample" style="background: #7f5af0;"></div>
    </div>
    
    <div class="theme-preview dark-theme">
        <div class="theme-name">2. Dark Theme (Tema Escuro) - DEFAULT</div>
        <div class="theme-description">Easy on the eyes for low-light environments</div>
        <div class="color-sample" style="background: #111827;"></div>
        <div class="color-sample" style="background: #f9fafb;"></div>
        <div class="color-sample" style="background: #7f5af0;"></div>
    </div>
    
    <div class="theme-preview sepia-theme">
        <div class="theme-name">3. Sepia Theme (Tema Sépia)</div>
        <div class="theme-description">Warm, paper-like appearance for comfortable reading</div>
        <div class="color-sample" style="background: #f4ecd8;"></div>
        <div class="color-sample" style="background: #5b4636;"></div>
        <div class="color-sample" style="background: #c19a6b;"></div>
    </div>
    
    <div class="theme-preview ocean-theme">
        <div class="theme-name">4. Ocean Theme (Tema Oceano) - NEW</div>
        <div class="theme-description">Cool blue tones inspired by deep ocean waters</div>
        <div class="color-sample" style="background: #0f172a;"></div>
        <div class="color-sample" style="background: #e2e8f0;"></div>
        <div class="color-sample" style="background: #0ea5e9;"></div>
    </div>
    
    <div class="theme-preview forest-theme">
        <div class="theme-name">5. Forest Theme (Tema Floresta) - NEW</div>
        <div class="theme-description">Natural green tones for a calming reading experience</div>
        <div class="color-sample" style="background: #14532d;"></div>
        <div class="color-sample" style="background: #f0fdf4;"></div>
        <div class="color-sample" style="background: #22c55e;"></div>
    </div>
    
    <div class="theme-preview sunset-theme">
        <div class="theme-name">6. Sunset Theme (Tema Pôr do Sol) - NEW</div>
        <div class="theme-description">Warm orange and red tones for evening reading</div>
        <div class="color-sample" style="background: #7c2d12;"></div>
        <div class="color-sample" style="background: #fef7ed;"></div>
        <div class="color-sample" style="background: #fb923c;"></div>
    </div>
    
    <h2>Theme Cycling Order</h2>
    <p>The theme toggle button cycles through themes in this order:</p>
    <ol>
        <li>Light → Dark</li>
        <li>Dark → Sepia</li>
        <li>Sepia → Ocean</li>
        <li>Ocean → Forest</li>
        <li>Forest → Sunset</li>
        <li>Sunset → Light</li>
    </ol>
    
    <h2>Features Implemented</h2>
    <ul>
        <li>✅ 6 total themes (3 existing + 3 new)</li>
        <li>✅ Dark theme as default</li>
        <li>✅ Theme-specific icons in toggle button</li>
        <li>✅ Theme-specific law content styling</li>
        <li>✅ localStorage persistence</li>
        <li>✅ Mobile-friendly performance</li>
        <li>✅ Compatibility with highlighting functionality</li>
        <li>✅ Hierarchical navigation support</li>
    </ul>
</body>
</html>
