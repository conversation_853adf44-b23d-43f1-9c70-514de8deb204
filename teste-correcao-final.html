<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste Correção Final</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a9e;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            max-height: 400px;
            overflow-y: auto;
        }
        
        /* Estilos dos títulos */
        .combined-title {
            padding: 0.7rem 1rem !important;
            margin: 1.5rem 0 1rem 0 !important;
            border-radius: 4px !important;
            font-weight: 600 !important;
            font-size: 0.95em !important;
            letter-spacing: 0.3px !important;
            text-transform: uppercase !important;
            color: #f1f5f9 !important;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12) !important;
            text-align: left !important;
            line-height: 1.3 !important;
            border-left: 3px solid !important;
        }
        
        .title-livro {
            border-left-color: #dc2626 !important;
            background: rgba(127, 29, 29, 0.85) !important;
        }
        
        .title-parte {
            border-left-color: #7c3aed !important;
            background: rgba(76, 29, 149, 0.85) !important;
        }
        
        .title-titulo {
            border-left-color: #2563eb !important;
            background: rgba(30, 58, 138, 0.85) !important;
        }
        
        .title-capitulo {
            border-left-color: #10b981 !important;
            background: rgba(6, 78, 59, 0.85) !important;
        }
        
        .title-secao {
            border-left-color: #f59e0b !important;
            background: rgba(146, 64, 14, 0.85) !important;
        }
        
        .heading {
            background: rgba(251, 191, 36, 0.08) !important;
            border-left: 4px solid #fde68a !important;
            padding: 0.5em 1em !important;
            margin-top: 2em !important;
            margin-bottom: 1.2em !important;
            border-radius: 0.4em !important;
            font-weight: 600 !important;
        }
        
        .normal-text {
            padding: 0.5em;
            margin: 0.5em 0;
            background: #f8f9fa;
            border-left: 2px solid #dee2e6;
        }
    </style>
</head>
<body>
    <h1>Teste Correção Final - Problema dos Títulos Individuais</h1>
    
    <div class="container">
        <h2>Estrutura Problemática (antes da correção)</h2>
        <p><strong>Problema:</strong> "Alegação", "Da Produção" apareciam como títulos individuais quando não deveriam.</p>
        
        <div id="test-container">
            <!-- Estrutura exata do problema -->
            <p class="heading">CAPÍTULO I</p>
            <p>Da Personalidade e da Capacidade</p>
            
            <p class="heading">SEÇÃO I</p>
            <p>Das Regras Gerais</p>
            
            <p class="heading">Subseção I</p>
            <p>Da Produção da Prova Testemunhal</p>
            
            <!-- Estes NÃO devem virar títulos individuais -->
            <p class="heading">Alegação</p>
            <p>Das Regras Gerais</p>
            
            <p class="heading">Da Produção</p>
            <p>Dos Procedimentos Específicos</p>
            
            <!-- Texto normal que não deve ser afetado -->
            <p>Art. 1º Este é um artigo normal.</p>
            <p>§ 1º Este é um parágrafo normal.</p>
            <p>Da aplicação das normas processuais.</p>
        </div>
        
        <button onclick="testarCorrecao()">Testar Correção</button>
        <button onclick="resetTest()">Resetar</button>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        function showResult(message) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.style.display = 'block';
        }

        function getTitleType(text) {
            const upperText = text.toUpperCase();
            if (upperText.includes('LIVRO')) return 'title-livro';
            if (upperText.includes('TÍTULO') || upperText.includes('TITULO')) return 'title-titulo';
            if (upperText.includes('CAPÍTULO') || upperText.includes('CAPITULO')) return 'title-capitulo';
            if (upperText.includes('SEÇÃO') || upperText.includes('SECAO')) return 'title-secao';
            if (upperText.includes('SUBSEÇÃO') || upperText.includes('SUBSECAO')) return 'title-secao';
            if (upperText.includes('PARTE')) return 'title-parte';
            // Subseções menores
            if (/^(ALEGAÇÃO|DA\s+\w+|DO\s+\w+|DAS\s+\w+|DOS\s+\w+|NA\s+\w+|NO\s+\w+)/i.test(text)) return 'title-secao';
            // Itens numerados
            if (/^[IVX]+\s*[-–]\s*\w+/i.test(text) || /^\d+\s*[-–]\s*\w+/i.test(text)) return 'title-secao';
            // Subseção específica
            if (/^Subse[çc][ãa]o\s+[IVX]+$/i.test(text)) return 'title-secao';
            return 'title-default';
        }

        function testarCorrecao() {
            const container = document.getElementById('test-container');
            const paragraphs = Array.from(container.querySelectorAll('p'));
            
            let logs = 'TESTE DA CORREÇÃO:\n\n';
            logs += `📊 Total paragraphs: ${paragraphs.length}\n\n`;
            
            // Padrões corrigidos
            const patterns = {
                // PADRÃO CORRIGIDO: apenas títulos principais
                processedTitlePattern: /^(LIVRO|PARTE|T[ÍI]TULO|CAP[ÍI]TULO|SE[ÇC][ÃA]O|SUBSE[ÇC][ÃA]O)\s+/i
            };
            
            const toRemove = [];
            let combinedCount = 0;
            
            // FASE 1: Processar combinações (título + descrição)
            logs += '🔄 FASE 1: Processando combinações\n';
            for (let i = 0; i < paragraphs.length - 1; i++) {
                const current = paragraphs[i];
                const next = paragraphs[i + 1];
                
                if (toRemove.includes(current) || toRemove.includes(next)) continue;
                
                const currentText = current.textContent?.trim() || '';
                const nextText = next.textContent?.trim() || '';
                
                // Verificar condições
                const isCurrentHeading = current.classList.contains('heading');
                const isCurrentTitle = /^(LIVRO|PARTE|T[ÍI]TULO|CAP[ÍI]TULO|SE[ÇC][ÃA]O|SUBSE[ÇC][ÃA]O)\s+/i.test(currentText);
                const isSubsection = /^(Alegação|Da\s+\w+|Do\s+\w+|Das\s+\w+|Dos\s+\w+|Na\s+\w+|No\s+\w+|Disposições?\s+\w+|Normas?\s+\w+|Regras?\s+\w+)$/i.test(currentText) ||
                                   /^[IVX]+\s*[-–]\s*\w+/i.test(currentText) ||
                                   /^\d+\s*[-–]\s*\w+/i.test(currentText) ||
                                   /^Subse[çc][ãa]o\s+[IVX]+$/i.test(currentText);
                const hasNextText = nextText && nextText.length > 2;
                const nextNotHeading = !next.classList.contains('heading');
                const nextNotArticle = !/^Art\.|^§|^\d+[º°]/.test(nextText);
                
                const shouldCombine = (isCurrentHeading && (isCurrentTitle || isSubsection)) && hasNextText && nextNotHeading && nextNotArticle;
                
                if (shouldCombine) {
                    const combinedTitle = `${currentText.toUpperCase()} - ${nextText.toUpperCase()}`;
                    const titleType = getTitleType(currentText);
                    
                    logs += `✅ COMBINANDO: "${currentText}" + "${nextText}" -> ${titleType}\n`;
                    
                    current.innerHTML = combinedTitle;
                    current.className = `combined-title ${titleType}`;
                    toRemove.push(next);
                    combinedCount++;
                }
            }
            
            // Remover elementos combinados
            toRemove.forEach(element => element.remove());
            
            // FASE 2: Processar títulos individuais (CORRIGIDO)
            logs += `\n🔄 FASE 2: Processando títulos individuais (PADRÃO CORRIGIDO)\n`;
            const remainingParagraphs = Array.from(container.querySelectorAll('p'));
            let individualCount = 0;
            
            remainingParagraphs.forEach(p => {
                const text = p.textContent?.trim() || '';
                
                // TESTE DO PADRÃO CORRIGIDO
                const matchesPattern = patterns.processedTitlePattern.test(text);
                const hasHeadingClass = p.classList.contains('heading');
                const notAlreadyCombined = !p.classList.contains('combined-title');
                
                logs += `📝 Testando: "${text}"\n`;
                logs += `  - Matches pattern: ${matchesPattern}\n`;
                logs += `  - Has heading class: ${hasHeadingClass}\n`;
                logs += `  - Not combined: ${notAlreadyCombined}\n`;
                
                if (matchesPattern && notAlreadyCombined && hasHeadingClass) {
                    const titleType = getTitleType(text);
                    p.classList.add('combined-title', titleType);
                    p.innerHTML = text.toUpperCase();
                    individualCount++;
                    logs += `  ✅ PROCESSADO como título individual -> ${titleType}\n`;
                } else {
                    logs += `  ❌ NÃO processado (correto!)\n`;
                }
                logs += '\n';
            });
            
            // Marcar textos normais
            const finalParagraphs = Array.from(container.querySelectorAll('p'));
            finalParagraphs.forEach(p => {
                if (!p.classList.contains('combined-title') && !p.classList.contains('heading')) {
                    p.classList.add('normal-text');
                }
            });
            
            // Verificar resultado final
            const finalTitles = container.querySelectorAll('.combined-title');
            logs += `\n🎯 RESULTADO FINAL:\n`;
            logs += `📝 Títulos combinados: ${combinedCount}\n`;
            logs += `📝 Títulos individuais: ${individualCount}\n`;
            logs += `📝 Total títulos: ${finalTitles.length}\n`;
            logs += `🗑️ Elementos removidos: ${toRemove.length}\n\n`;
            
            logs += `📋 TÍTULOS FINAIS:\n`;
            finalTitles.forEach((title, index) => {
                const className = title.className.split(' ').find(c => c.startsWith('title-'));
                const color = getColorName(className);
                logs += `${index + 1}. "${title.textContent}" -> ${color}\n`;
            });
            
            // Verificar se "Alegação" e "Da Produção" NÃO viraram títulos
            const alegacaoTitle = Array.from(finalTitles).find(t => t.textContent?.includes('ALEGAÇÃO'));
            const daProducaoTitle = Array.from(finalTitles).find(t => t.textContent?.includes('DA PRODUÇÃO') && !t.textContent?.includes('SUBSEÇÃO'));
            
            logs += `\n🔍 VERIFICAÇÃO ESPECÍFICA:\n`;
            logs += `❌ "Alegação" como título individual: ${alegacaoTitle ? 'SIM (PROBLEMA!)' : 'NÃO (CORRETO!)'}\n`;
            logs += `❌ "Da Produção" como título individual: ${daProducaoTitle ? 'SIM (PROBLEMA!)' : 'NÃO (CORRETO!)'}\n`;
            
            if (!alegacaoTitle && !daProducaoTitle) {
                logs += `\n🎉 CORREÇÃO FUNCIONOU! Títulos problemáticos não foram processados individualmente.\n`;
            } else {
                logs += `\n⚠️ PROBLEMA AINDA EXISTE! Alguns títulos foram processados incorretamente.\n`;
            }
            
            showResult(logs);
        }

        function getColorName(className) {
            switch(className) {
                case 'title-livro': return '🔴 VERMELHO (LIVRO)';
                case 'title-parte': return '🟣 ROXO (PARTE)';
                case 'title-titulo': return '🔵 AZUL (TÍTULO)';
                case 'title-capitulo': return '🟢 VERDE (CAPÍTULO)';
                case 'title-secao': return '🟠 LARANJA (SEÇÃO/SUBSEÇÃO)';
                default: return '⚪ PADRÃO';
            }
        }

        function resetTest() {
            const container = document.getElementById('test-container');
            container.innerHTML = `
                <p class="heading">CAPÍTULO I</p>
                <p>Da Personalidade e da Capacidade</p>
                
                <p class="heading">SEÇÃO I</p>
                <p>Das Regras Gerais</p>
                
                <p class="heading">Subseção I</p>
                <p>Da Produção da Prova Testemunhal</p>
                
                <p class="heading">Alegação</p>
                <p>Das Regras Gerais</p>
                
                <p class="heading">Da Produção</p>
                <p>Dos Procedimentos Específicos</p>
                
                <p>Art. 1º Este é um artigo normal.</p>
                <p>§ 1º Este é um parágrafo normal.</p>
                <p>Da aplicação das normas processuais.</p>
            `;
            showResult('Teste resetado para o estado inicial.');
        }
    </script>
</body>
</html>
