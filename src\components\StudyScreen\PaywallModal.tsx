import React from 'react';
import type { PaywallModalProps } from '../../types/lawView';

export const PaywallModal: React.FC<PaywallModalProps> = ({
  isVisible,
  creditsLeft,
  navigate,
  onDismiss,
}) => {
  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black/70 z-50 flex items-center justify-center">
      <div className="modal-bg p-6 rounded-lg shadow-xl max-w-md text-center">
        <h3 className="text-lg font-bold">Acesso completo?</h3>
        <p className="text-sm opacity-80 mt-2">
          Crie uma conta para grifos e IA ilimitados, ou continue no modo de teste com {creditsLeft} créditos restantes.
        </p>
        <div className="mt-4 flex gap-2 justify-center">
          <button 
            onClick={() => navigate('/login')} 
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Criar conta
          </button>
          <button 
            onClick={onDismiss} 
            className="px-4 py-2 bg-neutral-700 rounded hover:bg-neutral-600"
          >
            Continuar teste
          </button>
        </div>
      </div>
    </div>
  );
};
