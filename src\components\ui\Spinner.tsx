import React from "react";

interface SpinnerProps {
  message?: string;
  subMessage?: string;
}

export const Spinner: React.FC<SpinnerProps> = ({ message, subMessage }) => (
  <div className="app-spinner">
    <svg className="animate-spin h-8 w-8 text-blue-400" viewBox="0 0 24 24">
      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path>
    </svg>
    {message && <span className="text-sm text-center">{message}</span>}
    {subMessage && (
      <span className="text-[10px] opacity-70 text-center whitespace-pre-line">
        {subMessage}
      </span>
    )}
  </div>
); 