import React, { useState } from 'react';
import { X, ChevronRight, ChevronDown, FileText, Loader2 } from 'lucide-react';
import Modal from './ui/Modal';
import { SystematicIndexItem } from '../hooks/useSystematicIndex';

interface SystematicIndexProps {
  isOpen: boolean;
  onClose: () => void;
  lawName: string;
  items: SystematicIndexItem[];
  onItemClick?: (item: SystematicIndexItem) => void;
  isLoading?: boolean;
}

interface IndexItemProps {
  item: SystematicIndexItem;
  onItemClick?: (item: SystematicIndexItem) => void;
  level: number;
}

const IndexItem: React.FC<IndexItemProps> = ({ item, onItemClick, level }) => {
  const [isExpanded, setIsExpanded] = useState(level < 2); // Expandir automaticamente os primeiros 2 níveis

  const hasChildren = item.children && item.children.length > 0;

  // Cores baseadas no tipo de elemento (similar à imagem)
  const getTypeColor = (type: string) => {
    switch (type) {
      case 'livro':
        return 'text-red-400';
      case 'titulo':
        return 'text-green-400';
      case 'capitulo':
        return 'text-purple-400';
      case 'secao':
        return 'text-orange-400';
      case 'subsecao':
        return 'text-yellow-400';
      case 'artigo':
        return 'text-blue-400';
      default:
        return 'text-gray-400';
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'livro':
        return 'LIVRO';
      case 'titulo':
        return 'TÍTULO';
      case 'capitulo':
        return 'CAPÍTULO';
      case 'secao':
        return 'SEÇÃO';
      case 'subsecao':
        return 'SUBSEÇÃO';
      case 'artigo':
        return 'ART.';
      default:
        return '';
    }
  };

  const handleClick = () => {
    if (hasChildren) {
      setIsExpanded(!isExpanded);
    }
    if (onItemClick) {
      onItemClick(item);
    }
  };

  const handleNavigateClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onItemClick) {
      onItemClick(item);
    }
  };

  return (
    <div className="select-none">
      <div
        className={`flex items-center justify-between py-2 px-3 rounded-md hover:bg-gray-700/50 cursor-pointer transition-colors group ${
          level > 0 ? `ml-${level * 2}` : ''
        }`}
        onClick={handleClick}
      >
        <div className="flex items-center gap-3 flex-1 min-w-0">
          {hasChildren && (
            <div className="flex-shrink-0">
              {isExpanded ? (
                <ChevronDown size={16} className="text-gray-400" />
              ) : (
                <ChevronRight size={16} className="text-gray-400" />
              )}
            </div>
          )}
          
          {!hasChildren && item.type === 'artigo' && (
            <div className="flex-shrink-0 ml-4">
              <FileText size={14} className="text-gray-500" />
            </div>
          )}

          <div className="flex items-center gap-2 min-w-0 flex-1">
            <span className={`text-xs font-semibold uppercase tracking-wide ${getTypeColor(item.type)} flex-shrink-0`}>
              {getTypeLabel(item.type)}
            </span>
            <span className="text-sm text-gray-200 truncate">
              {item.title.replace(/^(LIVRO|TÍTULO|CAPÍTULO|SEÇÃO|SUBSEÇÃO|Art\.?)\s*/i, '')}
            </span>
            {hasChildren && (
              <span className="text-xs text-gray-500 bg-gray-700 px-1.5 py-0.5 rounded-full">
                {item.children!.length}
              </span>
            )}
          </div>
        </div>

        <button
          onClick={handleNavigateClick}
          className="p-1 rounded hover:bg-gray-600 opacity-0 group-hover:opacity-100 transition-opacity flex-shrink-0"
          title="Navegar para esta seção"
        >
          <ChevronRight size={16} className="text-gray-400" />
        </button>
      </div>

      {hasChildren && isExpanded && (
        <div className="border-l border-gray-600 ml-4 pl-2">
          {item.children!.map((child, index) => (
            <IndexItem
              key={child.id}
              item={child}
              onItemClick={onItemClick}
              level={level + 1}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export const SystematicIndex: React.FC<SystematicIndexProps> = ({
  isOpen,
  onClose,
  lawName,
  items,
  onItemClick,
  isLoading = false
}) => {
  const totalItems = items.reduce((count, item) => {
    const countChildren = (children: SystematicIndexItem[]): number => {
      return children.reduce((acc, child) => {
        return acc + 1 + (child.children ? countChildren(child.children) : 0);
      }, 0);
    };
    return count + 1 + (item.children ? countChildren(item.children) : 0);
  }, 0);

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="lg">
      <div className="bg-gray-800 text-white rounded-lg max-h-[85vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-700">
          <div className="flex items-center gap-3">
            <FileText size={20} className="text-blue-400" />
            <h2 className="text-lg font-semibold truncate">{lawName}</h2>
          </div>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-700 rounded-md transition-colors"
          >
            <X size={20} className="text-gray-400" />
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-4">
          {isLoading ? (
            <div className="text-center py-8 text-gray-400">
              <Loader2 size={48} className="mx-auto mb-4 animate-spin" />
              <p>Carregando estrutura da lei...</p>
              <p className="text-sm mt-2">Analisando hierarquia do documento</p>
            </div>
          ) : items.length === 0 ? (
            <div className="text-center py-8 text-gray-400">
              <FileText size={48} className="mx-auto mb-4 opacity-50" />
              <p>Nenhuma estrutura hierárquica encontrada</p>
              <p className="text-sm mt-2">Esta lei pode não ter uma estrutura sistemática detectável.</p>
            </div>
          ) : (
            <div className="space-y-1">
              {items.map((item) => (
                <IndexItem
                  key={item.id}
                  item={item}
                  onItemClick={onItemClick}
                  level={0}
                />
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-gray-700">
          <div className="flex items-center justify-between text-xs text-gray-400">
            <span>
              {totalItems > 0 ? `${totalItems} itens encontrados` : 'Estrutura não detectada'}
            </span>
            <span className="text-gray-500">
              Clique nos itens para navegar
            </span>
          </div>
        </div>
      </div>
    </Modal>
  );
};
