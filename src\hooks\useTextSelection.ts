import { useState, useEffect, useCallback } from 'react';

interface SerializedRange {
  startContainerPath: number[];
  startOffset: number;
  endContainerPath: number[];
  endOffset: number;
  text: string;
}

function getElementPath(element: Node, root: Node): number[] {
  const path: number[] = [];
  let current = element;
  while (current && current !== root) {
    const parent = current.parentNode;
    if (!parent) break;
    const index = Array.from(parent.childNodes).indexOf(current as ChildNode);
    path.unshift(index);
    current = parent;
  }
  return path;
}

export function getNodeFromPath(path: number[], root: Node): Node | null {
  let current: Node | null = root;
  for (const index of path) {
    if (!current || !current.childNodes[index]) {
      current = null;
      break;
    }
    current = current.childNodes[index];
  }
  return current;
}

/**
 * Valida se uma seleção está dentro de um único artigo e é adequada para grifo
 */
export function validateSelection(selection: Selection, container: Element): {
  isValid: boolean;
  reason?: string;
  articleId?: string;
} {
  if (!selection.rangeCount || selection.isCollapsed) {
    return { isValid: false, reason: 'Nenhuma seleção válida' };
  }

  const range = selection.getRangeAt(0);
  const startContainer = range.startContainer;
  const endContainer = range.endContainer;

  // Encontra o artigo do início e fim da seleção
  const startArticle = findNearestArticle(startContainer, container);
  const endArticle = findNearestArticle(endContainer, container);

  // Verifica se ambos estão no mesmo artigo
  if (startArticle !== endArticle) {
    return {
      isValid: false,
      reason: 'Seleção não pode atravessar múltiplos artigos. Selecione texto dentro de um único artigo.'
    };
  }

  // Verifica se a seleção não está muito longa (mais de 500 caracteres)
  const selectedText = selection.toString().trim();
  if (selectedText.length > 500) {
    return {
      isValid: false,
      reason: 'Seleção muito longa. Limite: 500 caracteres.'
    };
  }

  // Verifica se não está selecionando apenas espaços
  if (selectedText.length < 3) {
    return {
      isValid: false,
      reason: 'Seleção muito curta. Selecione pelo menos 3 caracteres.'
    };
  }

  return {
    isValid: true,
    articleId: startArticle
  };
}

/**
 * Encontra o artigo mais próximo de um nó
 */
function findNearestArticle(node: Node, container: Element): string {
  let current = node.nodeType === Node.TEXT_NODE ? node.parentElement : node as Element;

  while (current && current !== container) {
    // Procura por elementos com ID que começam com "art"
    if (current.id && current.id.match(/^art\d+/i)) {
      return current.id;
    }

    // Procura por elementos que contenham "Art." no texto
    if (current.textContent && current.textContent.match(/Art\.?\s*\d+/i)) {
      const match = current.textContent.match(/Art\.?\s*(\d+)/i);
      if (match) {
        return `art${match[1]}`;
      }
    }

    current = current.parentElement;
  }

  return 'unknown';
}

export function useTextSelection(containerRef: React.RefObject<HTMLElement>) {
  const [serializedSelection, setSerializedSelection] = useState<SerializedRange | null>(null);

  const clearSelection = useCallback(() => {
    window.getSelection()?.removeAllRanges();
    setSerializedSelection(null);
  }, []);

  const handleSelectionChange = useCallback(() => {
    const sel = window.getSelection();
    if (!sel || sel.rangeCount === 0 || sel.isCollapsed) {
      setSerializedSelection(null);
      return;
    }

    const range = sel.getRangeAt(0);
    const text = sel.toString().trim();

    if (text.length < 2 || !containerRef.current || !containerRef.current.contains(range.commonAncestorContainer)) {
      setSerializedSelection(null);
      return;
    }

    const root = containerRef.current;
    const startContainerPath = getElementPath(range.startContainer, root);
    const endContainerPath = getElementPath(range.endContainer, root);

    setSerializedSelection({
      startContainerPath,
      startOffset: range.startOffset,
      endContainerPath,
      endOffset: range.endOffset,
      text,
    });
  }, [containerRef]);

  useEffect(() => {
    const onSelectionChange = () => handleSelectionChange();
    document.addEventListener('selectionchange', onSelectionChange);
    return () => {
      document.removeEventListener('selectionchange', onSelectionChange);
    };
  }, [handleSelectionChange]);

  return { serializedSelection, clearSelection, getNodeFromPath };
}
