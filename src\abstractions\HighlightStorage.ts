import type { Highlight } from '../hooks/useHighlights';

/**
 * Abstract interface for highlight storage
 * Follows Open/Closed Principle - open for extension, closed for modification
 */
export interface IHighlightStorage {
  load(leiId: string): Promise<Highlight[]>;
  save(leiId: string, highlights: Highlight[]): Promise<void>;
  add(leiId: string, highlight: Highlight): Promise<void>;
  remove(leiId: string, highlightId: string): Promise<void>;
  subscribe(leiId: string, callback: (highlights: Highlight[]) => void): () => void;
}

/**
 * Local Storage implementation
 */
export class LocalStorageHighlightStorage implements IHighlightStorage {
  private getKey(leiId: string, userId: string = 'guest'): string {
    return `grifos-${userId}-${leiId}`;
  }

  async load(leiId: string): Promise<Highlight[]> {
    try {
      const key = this.getKey(leiId);
      const raw = localStorage.getItem(key);
      if (!raw) return [];
      
      const parsed = JSON.parse(raw);
      if (!Array.isArray(parsed)) return [];
      
      return parsed.filter(h => 
        h && h.id && h.startContainerPath && h.endContainerPath
      );
    } catch (error) {
      console.error('[LocalStorageHighlightStorage] Error loading highlights:', error);
      return [];
    }
  }

  async save(leiId: string, highlights: Highlight[]): Promise<void> {
    try {
      const key = this.getKey(leiId);
      localStorage.setItem(key, JSON.stringify(highlights));
    } catch (error) {
      console.error('[LocalStorageHighlightStorage] Error saving highlights:', error);
      throw error;
    }
  }

  async add(leiId: string, highlight: Highlight): Promise<void> {
    const highlights = await this.load(leiId);
    highlights.push(highlight);
    await this.save(leiId, highlights);
  }

  async remove(leiId: string, highlightId: string): Promise<void> {
    const highlights = await this.load(leiId);
    const filtered = highlights.filter(h => h.id !== highlightId);
    await this.save(leiId, filtered);
  }

  subscribe(leiId: string, callback: (highlights: Highlight[]) => void): () => void {
    // For localStorage, we can use storage events
    const handleStorageChange = (e: StorageEvent) => {
      const key = this.getKey(leiId);
      if (e.key === key) {
        this.load(leiId).then(callback);
      }
    };

    window.addEventListener('storage', handleStorageChange);
    
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }
}

/**
 * Firestore implementation
 */
export class FirestoreHighlightStorage implements IHighlightStorage {
  constructor(
    private db: any,
    private userId: string
  ) {}

  async load(leiId: string): Promise<Highlight[]> {
    try {
      const { collection, getDocs } = await import('firebase/firestore');
      const colRef = collection(this.db, "grifos", this.userId, leiId);
      const snapshot = await getDocs(colRef);
      
      const highlights: Highlight[] = [];
      snapshot.forEach((doc) => {
        const data = doc.data();
        if (data.startContainerPath && data.endContainerPath) {
          highlights.push({ id: doc.id, ...data } as Highlight);
        }
      });
      
      return highlights;
    } catch (error) {
      console.error('[FirestoreHighlightStorage] Error loading highlights:', error);
      return [];
    }
  }

  async save(leiId: string, highlights: Highlight[]): Promise<void> {
    // For Firestore, we typically don't bulk save, but add/remove individually
    throw new Error('Bulk save not supported for Firestore. Use add/remove methods.');
  }

  async add(leiId: string, highlight: Highlight): Promise<void> {
    try {
      const { collection, addDoc } = await import('firebase/firestore');
      const colRef = collection(this.db, "grifos", this.userId, leiId);
      await addDoc(colRef, highlight);
    } catch (error) {
      console.error('[FirestoreHighlightStorage] Error adding highlight:', error);
      throw error;
    }
  }

  async remove(leiId: string, highlightId: string): Promise<void> {
    try {
      const { doc, deleteDoc } = await import('firebase/firestore');
      const docRef = doc(this.db, "grifos", this.userId, leiId, highlightId);
      await deleteDoc(docRef);
    } catch (error) {
      console.error('[FirestoreHighlightStorage] Error removing highlight:', error);
      throw error;
    }
  }

  subscribe(leiId: string, callback: (highlights: Highlight[]) => void): () => void {
    const setupSubscription = async () => {
      try {
        const { collection, onSnapshot } = await import('firebase/firestore');
        const colRef = collection(this.db, "grifos", this.userId, leiId);
        
        return onSnapshot(colRef, (snapshot) => {
          const highlights: Highlight[] = [];
          snapshot.forEach((doc) => {
            const data = doc.data();
            if (data.startContainerPath && data.endContainerPath) {
              highlights.push({ id: doc.id, ...data } as Highlight);
            }
          });
          callback(highlights);
        }, (error) => {
          console.error('[FirestoreHighlightStorage] Subscription error:', error);
          callback([]);
        });
      } catch (error) {
        console.error('[FirestoreHighlightStorage] Error setting up subscription:', error);
        return () => {};
      }
    };

    let unsubscribe: (() => void) | null = null;
    setupSubscription().then(unsub => {
      unsubscribe = unsub;
    });

    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }
}

/**
 * Factory for creating storage instances
 * Follows Factory pattern and Open/Closed Principle
 */
export class HighlightStorageFactory {
  static create(type: 'localStorage' | 'firestore', options?: any): IHighlightStorage {
    switch (type) {
      case 'localStorage':
        return new LocalStorageHighlightStorage();
      case 'firestore':
        if (!options?.db || !options?.userId) {
          throw new Error('Firestore storage requires db and userId options');
        }
        return new FirestoreHighlightStorage(options.db, options.userId);
      default:
        throw new Error(`Unknown storage type: ${type}`);
    }
  }
}
