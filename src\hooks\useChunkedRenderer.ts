import { useState, useMemo, useRef, useEffect, useCallback } from 'react';

const CHUNK_SIZE = 50;
const MAX_VISIBLE_CHUNKS = 4;

/**
 * Hook para renderização em chunks de um texto longo (leis locais).
 * @param texto O texto completo a ser renderizado.
 * @returns Um objeto contendo os artigos visíveis e o handler de scroll.
 */
export function useChunkedRenderer(texto: string) {
  const artigos = useMemo(() => texto.split(/\n+/), [texto]);
  const [chunks, setChunks] = useState(1);
  const [firstChunk, setFirstChunk] = useState(0);

  const chunksRef = useRef(chunks);
  const firstChunkRef = useRef(0);

  useEffect(() => { chunksRef.current = chunks; }, [chunks]);
  useEffect(() => { firstChunkRef.current = firstChunk; }, [firstChunk]);

  const visibleArtigos = useMemo(() => {
    return artigos.slice(firstChunk * CHUNK_SIZE, (firstChunk + chunks) * CHUNK_SIZE);
  }, [artigos, firstChunk, chunks]);

  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
    const totalChunks = Math.ceil(artigos.length / CHUNK_SIZE);

    if (scrollHeight - scrollTop - clientHeight < 2000) { // Load next
      if (chunksRef.current < totalChunks) {
        setChunks(prev => Math.min(prev + 1, totalChunks));
      }
    }

    if (chunksRef.current > MAX_VISIBLE_CHUNKS) { // Unload previous
      setFirstChunk(prev => prev + 1);
      setChunks(prev => prev - 1);
    }
  }, [artigos.length]);

  return { visibleArtigos, handleScroll, firstChunkIndex: firstChunk * CHUNK_SIZE };
}
