import { <PERSON><PERSON><PERSON> } from "lucide-react";
import React, { useState, useRef, useEffect, ReactNode, isValidElement, cloneElement } from "react";

interface ColorPaletteProps {
  cores: string[];
  corAtiva: string;
  onChange: (cor: string) => void;
  badge?: React.ReactNode;
  children?: React.ReactNode;
}

export const ColorPalette: React.FC<ColorPaletteProps> = ({ cores, corAtiva, onChange, badge, children }) => {
  const [open, setOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!open) return;
    function handleClick(e: MouseEvent) {
      if (menuRef.current && !menuRef.current.contains(e.target as Node)) {
        setOpen(false);
      }
    }
    document.addEventListener("mousedown", handleClick);
    return () => document.removeEventListener("mousedown", handleClick);
  }, [open]);

  // Helper para padronizar children como botões
  function renderChildrenAsPaletteItems(children: ReactNode) {
    if (!children) return null;
    return React.Children.map(children, (child, idx) => {
      if (isValidElement(child) && typeof child.type === 'string') {
        // Se for elemento DOM (ex: button, a, span)
        return cloneElement(child, {
          ...child.props,
          key: idx,
          className: `w-7 h-7 rounded-full ring-2 flex items-center justify-center bg-white/10 text-white text-xs font-medium select-none transition-all duration-150 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-yellow-300 ring-white/10 ${child.props.className || ''}`.trim(),
          style: { minWidth: 28, minHeight: 28, ...(child.props.style || {}) },
        });
      }
      // Se for texto puro ou React.Fragment ou componente customizado
      return (
        <div
          key={idx}
          className="w-7 h-7 rounded-full ring-2 flex items-center justify-center bg-white/10 text-white text-xs font-medium select-none transition-all duration-150 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-yellow-300 ring-white/10"
          style={{ minWidth: 28, minHeight: 28 }}
        >
          {child}
        </div>
      );
    });
  }

  return (
    <div
      ref={menuRef}
      className="fixed bottom-4 left-1/2 -translate-x-1/2 z-50 flex items-center gap-1 px-2 py-1 sm:px-3 sm:py-2 palette-bg backdrop-blur-md rounded-full shadow-lg border border-white/20 max-w-full"
    >
      <button
        onClick={() => setOpen((v) => !v)}
        className="flex items-center justify-center p-0.5 rounded-full bg-transparent hover:bg-white/10 transition-colors focus:outline-none focus:ring-2 focus:ring-yellow-300 border border-white/10 shadow w-7 h-7"
        aria-label="Abrir menu de cores"
        aria-expanded={open}
        style={{ minWidth: 0 }}
      >
        <Highlighter size={15} className="text-yellow-300 drop-shadow-sm" />
      </button>
      {/* Cores visíveis em desktop, escondidas em mobile se menu aberto */}
      <div className={`hidden sm:flex items-center gap-1 sm:gap-2 transition-all duration-200 ${open ? 'opacity-40 pointer-events-none' : ''}`}>
        {cores.map((c) => (
          <button
            key={c}
            onClick={() => onChange(c)}
            className={`w-5 h-5 rounded-full ring-2 transition-all duration-150 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-yellow-300 ${corAtiva === c ? "ring-yellow-300 scale-110" : "ring-white/10"}`}
            style={{ backgroundColor: c }}
            aria-label={`Selecionar cor ${c}`}
          />
        ))}
      </div>
      {badge && (
        <div className="ml-1 flex items-center gap-1">{badge}</div>
      )}
      {open && (
        <div
          className="absolute left-1/2 bottom-12 -translate-x-1/2 z-50"
        >
          <div
            className="palette-popup-bg border border-yellow-300/30 rounded-2xl shadow-2xl p-2 flex flex-row flex-nowrap items-center gap-2 animate-slideUp min-w-[180px] max-w-[90vw] overflow-x-auto custom-scrollbar"
            style={{ WebkitOverflowScrolling: 'touch' }}
          >
            {/* Cores só aparecem no popover em telas pequenas */}
            <div className="flex flex-row flex-nowrap items-center gap-2 sm:hidden">
              {cores.map((c) => (
                <button
                  key={c}
                  onClick={() => { onChange(c); setOpen(false); }}
                  className={`w-7 h-7 rounded-full ring-2 transition-all duration-150 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-yellow-300 ${corAtiva === c ? "ring-yellow-300 scale-110" : "ring-yellow-300/30"}`}
                  style={{ backgroundColor: c }}
                  aria-label={`Selecionar cor ${c}`}
                />
              ))}
            </div>
            {/* Children padronizados como itens da paleta */}
            {renderChildrenAsPaletteItems(children)}
          </div>
        </div>
      )}
    </div>
  );
};

// Para animação, adicione ao seu tailwind.config.cjs:
// theme: { extend: { keyframes: { slideUp: { '0%': { opacity: 0, transform: 'translateY(12px)' }, '100%': { opacity: 1, transform: 'translateY(0)' } } }, animation: { slideUp: 'slideUp 0.14s cubic-bezier(0.4,0,0.2,1)' } } }

// Para barra de rolagem customizada igual à paleta, adicione ao seu CSS global (ex: index.css):
// .custom-scrollbar::-webkit-scrollbar {
//   height: 10px;
//   background: transparent;
// }
// .custom-scrollbar::-webkit-scrollbar-thumb {
//   background: #fde04766; /* amarelo-300 translúcido */
//   border-radius: 9999px;
//   border: 2px solid #27272a; /* cor do fundo do popover */
// }
// .custom-scrollbar::-webkit-scrollbar-track {
//   background: transparent;
// }
// /* Para Firefox */
// .custom-scrollbar {
//   scrollbar-color: #fde04766 transparent;
//   scrollbar-width: thin;
// }