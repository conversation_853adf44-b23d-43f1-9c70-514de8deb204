import React from 'react';

interface Props {
  show: boolean;
  position: { top: number; left: number };
  onConfirm: () => void;
}

export function HighlightMenu({ show, position, onConfirm }: Props) {
  if (!show) return null;

  return (
    <div
      style={{
        position: 'fixed',
        top: position.top,
        left: position.left,
        transform: 'translateX(-50%)',
        zIndex: 2000,
      }}
    >
      
      <button
        onMouseDown={(e)=>{e.preventDefault(); onConfirm();}}
        onTouchEnd={(e)=>{e.preventDefault(); onConfirm();}}
        onClick={(e)=>e.stopPropagation()}
        className="text-xs px-2 py-1 rounded bg-primary text-primary-foreground shadow-lg"
      >
        Grifar
      </button>
      
    </div>
  );
}
