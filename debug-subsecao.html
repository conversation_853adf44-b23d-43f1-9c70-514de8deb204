<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug - Subseção</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a9e;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            max-height: 400px;
            overflow-y: auto;
        }
        
        /* Estilos dos títulos */
        .combined-title {
            padding: 0.7rem 1rem !important;
            margin: 1.5rem 0 1rem 0 !important;
            border-radius: 4px !important;
            font-weight: 600 !important;
            font-size: 0.95em !important;
            letter-spacing: 0.3px !important;
            text-transform: uppercase !important;
            color: #f1f5f9 !important;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12) !important;
            text-align: left !important;
            line-height: 1.3 !important;
            border-left: 3px solid !important;
        }
        
        .title-secao {
            border-left-color: #f59e0b !important;
            background: rgba(146, 64, 14, 0.85) !important;
        }
        
        .title-capitulo {
            border-left-color: #10b981 !important;
            background: rgba(6, 78, 59, 0.85) !important;
        }
        
        .heading {
            background: rgba(251, 191, 36, 0.08) !important;
            border-left: 4px solid #fde68a !important;
            padding: 0.5em 1em !important;
            margin-top: 2em !important;
            margin-bottom: 1.2em !important;
            border-radius: 0.4em !important;
            font-weight: 600 !important;
        }
    </style>
</head>
<body>
    <h1>Debug - Subseção</h1>
    
    <div class="container">
        <h2>Teste específico para Subseção</h2>
        <div id="test-container">
            <!-- Estrutura exata do problema -->
            <p class="heading">Subseção I</p>
            <p>Da Produção da Prova Testemunhal</p>
            <p class="heading">CAPÍTULO II</p>
            <p>DO CUMPRIMENTO DEFINITIVO DA SENTENÇA QUE RECONHECE A EXIGIBILIDADE DE OBRIGAÇÃO DE PAGAR QUANTIA CERTA</p>
        </div>
        
        <button onclick="debugSubsecao()">Debug Subseção</button>
        <button onclick="resetTest()">Resetar</button>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        function showResult(message) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.style.display = 'block';
        }

        function getTitleType(text) {
            const upperText = text.toUpperCase();
            if (upperText.includes('LIVRO')) return 'title-livro';
            if (upperText.includes('TÍTULO') || upperText.includes('TITULO')) return 'title-titulo';
            if (upperText.includes('CAPÍTULO') || upperText.includes('CAPITULO')) return 'title-capitulo';
            if (upperText.includes('SEÇÃO') || upperText.includes('SECAO')) return 'title-secao';
            if (upperText.includes('SUBSEÇÃO') || upperText.includes('SUBSECAO')) return 'title-secao';
            if (upperText.includes('PARTE')) return 'title-parte';
            // Subseções menores
            if (/^(ALEGAÇÃO|DA\s+\w+|DO\s+\w+|DAS\s+\w+|DOS\s+\w+|NA\s+\w+|NO\s+\w+)/i.test(text)) return 'title-secao';
            // Itens numerados
            if (/^[IVX]+\s*[-–]\s*\w+/i.test(text) || /^\d+\s*[-–]\s*\w+/i.test(text)) return 'title-secao';
            // Subseção específica
            if (/^Subse[çc][ãa]o\s+[IVX]+$/i.test(text)) return 'title-secao';
            return 'title-default';
        }

        function debugSubsecao() {
            const container = document.getElementById('test-container');
            const paragraphs = Array.from(container.querySelectorAll('p'));
            
            let logs = 'DEBUG SUBSEÇÃO:\n\n';
            logs += `📊 Total paragraphs: ${paragraphs.length}\n\n`;
            
            // Mostrar estrutura inicial
            logs += 'ESTRUTURA INICIAL:\n';
            paragraphs.forEach((p, index) => {
                logs += `${index}: "${p.textContent}" (${p.className})\n`;
            });
            logs += '\n';
            
            const toRemove = [];
            
            // Processar cada par
            for (let i = 0; i < paragraphs.length - 1; i++) {
                const current = paragraphs[i];
                const next = paragraphs[i + 1];
                
                if (toRemove.includes(current) || toRemove.includes(next)) continue;
                
                const currentText = current.textContent?.trim() || '';
                const nextText = next.textContent?.trim() || '';
                
                logs += `🔍 Pair ${i}: "${currentText}" + "${nextText}"\n`;
                
                // Verificar condições detalhadamente
                const isCurrentHeading = current.classList.contains('heading');
                logs += `  isCurrentHeading: ${isCurrentHeading}\n`;
                
                // Teste específico para SUBSEÇÃO
                const isCurrentTitle = /^(LIVRO|PARTE|T[ÍI]TULO|CAP[ÍI]TULO|SE[ÇC][ÃA]O|SUBSE[ÇC][ÃA]O)\s+/i.test(currentText);
                logs += `  isCurrentTitle (regex): ${isCurrentTitle}\n`;
                logs += `  regex test: /^(LIVRO|PARTE|T[ÍI]TULO|CAP[ÍI]TULO|SE[ÇC][ÃA]O|SUBSE[ÇC][ÃA]O)\\s+/i\n`;
                logs += `  currentText: "${currentText}"\n`;
                
                // Teste manual para Subseção
                const manualSubsecaoTest = /^SUBSE[ÇC][ÃA]O\s+/i.test(currentText);
                logs += `  manual SUBSEÇÃO test: ${manualSubsecaoTest}\n`;
                
                // Teste para subseções genéricas
                const isSubsection = /^(Alegação|Da\s+\w+|Do\s+\w+|Das\s+\w+|Dos\s+\w+|Na\s+\w+|No\s+\w+|Disposições?\s+\w+|Normas?\s+\w+|Regras?\s+\w+)$/i.test(currentText) ||
                                   /^[IVX]+\s*[-–]\s*\w+/i.test(currentText) ||
                                   /^\d+\s*[-–]\s*\w+/i.test(currentText) ||
                                   /^Subse[çc][ãa]o\s+[IVX]+$/i.test(currentText) || // Subseção I, II, III, etc.
                                   (/^[A-ZÁÉÍÓÚÂÊÎÔÛÀÈÌÒÙÃÕÇ][a-záéíóúâêîôûàèìòùãõç]+(\s+[A-ZÁÉÍÓÚÂÊÎÔÛÀÈÌÒÙÃÕÇ][a-záéíóúâêîôûàèìòùãõç]+)*$/.test(currentText) &&
                                   currentText.length < 30 && !currentText.includes('Art.'));
                logs += `  isSubsection: ${isSubsection}\n`;
                
                const hasNextText = nextText && nextText.length > 2;
                const nextNotHeading = !next.classList.contains('heading');
                const nextNotArticle = !/^Art\.|^§|^\d+[º°]/.test(nextText);
                
                logs += `  hasNextText: ${hasNextText}\n`;
                logs += `  nextNotHeading: ${nextNotHeading}\n`;
                logs += `  nextNotArticle: ${nextNotArticle}\n`;
                
                const shouldCombine = (isCurrentHeading && (isCurrentTitle || isSubsection)) && hasNextText && nextNotHeading && nextNotArticle;
                logs += `  shouldCombine: ${shouldCombine}\n`;
                
                if (shouldCombine) {
                    const combinedTitle = `${currentText.toUpperCase()} - ${nextText.toUpperCase()}`;
                    const titleType = getTitleType(currentText);
                    
                    logs += `  ✅ COMBINING: "${combinedTitle}" -> ${titleType}\n`;
                    
                    current.innerHTML = combinedTitle;
                    current.className = `combined-title ${titleType}`;
                    toRemove.push(next);
                } else {
                    logs += `  ❌ Not combining\n`;
                }
                
                logs += '\n';
            }
            
            // Remover elementos
            toRemove.forEach(element => element.remove());
            logs += `🗑️ Removed ${toRemove.length} elements\n\n`;
            
            // Verificar resultado
            const finalTitles = container.querySelectorAll('.combined-title');
            logs += `✅ Final result: ${finalTitles.length} combined titles\n`;
            finalTitles.forEach((title, index) => {
                logs += `📝 Title ${index + 1}: "${title.textContent}" -> ${title.className}\n`;
            });
            
            showResult(logs);
        }

        function resetTest() {
            const container = document.getElementById('test-container');
            container.innerHTML = `
                <p class="heading">Subseção I</p>
                <p>Da Produção da Prova Testemunhal</p>
                <p class="heading">CAPÍTULO II</p>
                <p>DO CUMPRIMENTO DEFINITIVO DA SENTENÇA QUE RECONHECE A EXIGIBILIDADE DE OBRIGAÇÃO DE PAGAR QUANTIA CERTA</p>
            `;
            showResult('Teste resetado.');
        }
    </script>
</body>
</html>
