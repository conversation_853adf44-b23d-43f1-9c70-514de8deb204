<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste Simples Final</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .combined-title {
            padding: 0.7rem 1rem !important;
            margin: 1.5rem 0 1rem 0 !important;
            border-radius: 4px !important;
            font-weight: 600 !important;
            font-size: 0.95em !important;
            letter-spacing: 0.3px !important;
            text-transform: uppercase !important;
            color: #f1f5f9 !important;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12) !important;
            text-align: left !important;
            line-height: 1.3 !important;
            border-left: 3px solid !important;
        }
        
        .title-capitulo {
            border-left-color: #10b981 !important;
            background: rgba(6, 78, 59, 0.85) !important;
        }
        
        .title-secao {
            border-left-color: #f59e0b !important;
            background: rgba(146, 64, 14, 0.85) !important;
        }
        
        .heading {
            background: rgba(251, 191, 36, 0.08) !important;
            border-left: 4px solid #fde68a !important;
            padding: 0.5em 1em !important;
            margin-top: 2em !important;
            margin-bottom: 1.2em !important;
            border-radius: 0.4em !important;
            font-weight: 600 !important;
        }
    </style>
</head>
<body>
    <h1>Teste Simples Final</h1>
    
    <div class="container">
        <h2>Verificação da Correção</h2>
        <p><strong>Esperado:</strong> Apenas "CAPÍTULO I", "SEÇÃO I" e "SUBSEÇÃO I" devem virar títulos coloridos.</p>
        <p><strong>Problema:</strong> "Alegação" e "Da Produção" NÃO devem virar títulos individuais.</p>
        
        <div id="test-container">
            <p class="heading">CAPÍTULO I</p>
            <p>Da Personalidade e da Capacidade</p>
            
            <p class="heading">SEÇÃO I</p>
            <p>Das Regras Gerais</p>
            
            <p class="heading">Subseção I</p>
            <p>Da Produção da Prova Testemunhal</p>
            
            <!-- ESTES NÃO DEVEM VIRAR TÍTULOS -->
            <p class="heading">Alegação</p>
            <p>Das Regras Gerais</p>
            
            <p class="heading">Da Produção</p>
            <p>Dos Procedimentos Específicos</p>
        </div>
        
        <button onclick="testar()">Testar Correção</button>
        <button onclick="reset()">Resetar</button>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        function showResult(message) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.style.display = 'block';
        }

        function testar() {
            const container = document.getElementById('test-container');
            
            // PADRÃO CORRIGIDO - apenas títulos principais
            const processedTitlePattern = /^(LIVRO|PARTE|T[ÍI]TULO|CAP[ÍI]TULO|SE[ÇC][ÃA]O|SUBSE[ÇC][ÃA]O)\s+/i;
            
            let logs = 'TESTE DA CORREÇÃO:\n\n';
            
            // Testar cada parágrafo
            const paragraphs = Array.from(container.querySelectorAll('p'));
            paragraphs.forEach((p, index) => {
                const text = p.textContent?.trim() || '';
                const hasHeading = p.classList.contains('heading');
                const matchesPattern = processedTitlePattern.test(text);
                
                logs += `${index + 1}. "${text}"\n`;
                logs += `   - Has heading class: ${hasHeading}\n`;
                logs += `   - Matches pattern: ${matchesPattern}\n`;
                
                if (hasHeading && matchesPattern) {
                    logs += `   ✅ DEVE ser processado como título\n`;
                } else if (hasHeading && !matchesPattern) {
                    logs += `   ❌ NÃO deve ser processado (correto!)\n`;
                } else {
                    logs += `   📝 Texto normal\n`;
                }
                logs += '\n';
            });
            
            logs += '\n🎯 VERIFICAÇÃO ESPECÍFICA:\n';
            logs += `"CAPÍTULO I" deve ser título: ${processedTitlePattern.test('CAPÍTULO I')}\n`;
            logs += `"SEÇÃO I" deve ser título: ${processedTitlePattern.test('SEÇÃO I')}\n`;
            logs += `"Subseção I" deve ser título: ${processedTitlePattern.test('Subseção I')}\n`;
            logs += `"Alegação" deve ser título: ${processedTitlePattern.test('Alegação')} ❌\n`;
            logs += `"Da Produção" deve ser título: ${processedTitlePattern.test('Da Produção')} ❌\n`;
            
            const alegacaoMatch = processedTitlePattern.test('Alegação');
            const daProducaoMatch = processedTitlePattern.test('Da Produção');
            
            if (!alegacaoMatch && !daProducaoMatch) {
                logs += '\n🎉 CORREÇÃO FUNCIONOU! Padrões problemáticos não são mais detectados.\n';
            } else {
                logs += '\n⚠️ PROBLEMA AINDA EXISTE! Alguns padrões ainda são detectados incorretamente.\n';
            }
            
            showResult(logs);
        }

        function reset() {
            const container = document.getElementById('test-container');
            container.innerHTML = `
                <p class="heading">CAPÍTULO I</p>
                <p>Da Personalidade e da Capacidade</p>
                
                <p class="heading">SEÇÃO I</p>
                <p>Das Regras Gerais</p>
                
                <p class="heading">Subseção I</p>
                <p>Da Produção da Prova Testemunhal</p>
                
                <p class="heading">Alegação</p>
                <p>Das Regras Gerais</p>
                
                <p class="heading">Da Produção</p>
                <p>Dos Procedimentos Específicos</p>
            `;
            showResult('Teste resetado.');
        }
    </script>
</body>
</html>
