import { Link } from "react-router-dom";
import { MoreVertical, FileText } from "lucide-react";

interface Props {
  id: string;
  nome: string;
  descricao: string;
  onMenuClick: () => void; // Changed from onDelete to a more generic click handler
  onIndexClick?: () => void; // New handler for systematic index
  isCustom?: boolean;
  isDragging?: boolean;
}

export function LawCard({ id, nome, descricao, onMenuClick, onIndexClick, isCustom = false, isDragging = false }: Props) {
  return (
    <div className="relative group select-none">
      <Link
        draggable={false}
        to={`/lei/${id}`}
        className={`block rounded-lg p-4 hover:shadow-lg transition-shadow duration-200 card-bg min-h-[120px] ${
          isDragging ? "scale-105 transition-transform" : ""
        }`}
      >
        <div className="flex items-start justify-between mb-1">
          <h3 className="text-lg font-semibold truncate flex-1" title={nome}>{nome}</h3>
        </div>
        <p className="text-sm opacity-80 truncate" title={descricao}>{descricao}</p>
      </Link>

      <div className="absolute top-3 right-3 flex gap-1">
        {/* Systematic Index Button */}
        {onIndexClick && (
          <button
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              onIndexClick();
            }}
            className="p-1.5 rounded-full bg-blue-500/20 hover:bg-blue-500/40 text-blue-400 transition-all duration-200 hover:scale-110 opacity-80 hover:opacity-100"
            aria-label="Índice Sistemático"
            title="Ver índice sistemático da lei"
          >
            <FileText size={14} />
          </button>
        )}

        {/* Menu Button */}
        <button
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            if (onMenuClick) {
              onMenuClick();
            }
          }}
          className="p-1.5 rounded-full bg-neutral-200/60 hover:bg-neutral-300 text-neutral-800 transition-all duration-200 hover:scale-110 opacity-80 hover:opacity-100 dark:bg-neutral-700 dark:text-white"
          aria-label="Mais ações"
        >
          <MoreVertical size={14} />
        </button>
      </div>
    </div>
  );
}