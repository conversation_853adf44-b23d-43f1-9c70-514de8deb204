import { BASE_PATH } from './env';

export async function fetchLocalLawHtml(
  leiId: string,
  signal?: AbortSignal
): Promise<string | null> {
  try {
    // Use a more silent approach for expected 404s
    const res = await fetch(`${BASE_PATH}laws/${leiId}.html`, {
      signal,
      // Add cache control to avoid unnecessary network requests
      cache: 'no-cache'
    });

    // Handle 404 gracefully - this is expected for many laws
    if (!res.ok) {
      if (res.status === 404) {
        // Don't log anything for 404s as they're expected
        return null;
      } else {
        console.debug(`[fetchLocalLawHtml] Failed to fetch local file for ${leiId}: ${res.status}`);
      }
      return null;
    }

    const html = await res.text();

    // Check if this is a placeholder file indicating content is unavailable offline
    if (html.includes('indisponível offline') ||
        html.includes('unavailable offline') ||
        html.includes('Conteúdo da') && html.includes('indisponível')) {
      console.debug(`[fetchLocalLawHtml] Skipping placeholder file for ${leiId}`);
      return null;
    }

    // Check if the content is too short to be a real law (less than 1000 characters)
    if (html.length < 1000) {
      console.debug(`[fetchLocalLawHtml] Content too short for ${leiId}, likely placeholder`);
      return null;
    }

    console.debug(`[fetchLocalLawHtml] Successfully loaded local file for ${leiId}`);
    return html;
  } catch (error: any) {
    // Handle AbortError gracefully
    if (error.name === "AbortError" || error.message?.includes("aborted")) {
      console.debug(`[fetchLocalLawHtml] Request aborted for ${leiId} (this is normal)`);
    } else {
      console.debug(`[fetchLocalLawHtml] Error fetching local file for ${leiId}:`, error.message);
    }
    return null;
  }
}

