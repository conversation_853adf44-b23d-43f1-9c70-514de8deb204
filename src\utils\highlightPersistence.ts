import type { Highlight } from "../hooks/useHighlights";

/**
 * Utilitários para melhorar a persistência de highlights quando leis são atualizadas
 */

// Debug logging utility
const debugLog = (message: string, ...args: any[]) => {
  if (import.meta.env.DEV) {
    console.debug(`[HighlightPersistence] ${message}`, ...args);
  }
};

/**
 * Gera hash simples de uma string para detectar mudanças
 */
export function generateTextHash(text: string): string {
  let hash = 0;
  if (text.length === 0) return hash.toString();
  
  for (let i = 0; i < text.length; i++) {
    const char = text.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  
  return Math.abs(hash).toString(36);
}

/**
 * Extrai contexto antes e depois do texto selecionado
 */
export function extractContext(
  node: Node, 
  startOffset: number, 
  endOffset: number, 
  contextLength: number = 50
): { before: string; after: string } {
  if (!node || node.nodeType !== Node.TEXT_NODE) {
    return { before: '', after: '' };
  }

  const textContent = node.textContent || '';
  const before = textContent.substring(
    Math.max(0, startOffset - contextLength), 
    startOffset
  ).trim();
  
  const after = textContent.substring(
    endOffset, 
    Math.min(textContent.length, endOffset + contextLength)
  ).trim();

  return { before, after };
}

/**
 * Encontra o contexto do artigo mais próximo
 */
export function findArticleContext(node: Node, container: Element): string {
  let current = node.nodeType === Node.TEXT_NODE ? node.parentElement : node as Element;

  while (current && current !== container) {
    // Procura por elementos com ID que começam com "art"
    if (current.id && current.id.match(/^art\d+/i)) {
      return current.textContent?.substring(0, 200) || '';
    }

    // Procura por elementos que contenham "Art." no texto
    if (current.textContent && current.textContent.match(/Art\.?\s*\d+/i)) {
      return current.textContent.substring(0, 200);
    }

    current = current.parentElement;
  }

  return '';
}

/**
 * Calcula posição relativa do highlight dentro do artigo (0-1)
 */
export function calculateRelativePosition(
  node: Node, 
  startOffset: number, 
  container: Element
): number {
  try {
    // Encontra o artigo pai
    let articleElement = node.nodeType === Node.TEXT_NODE ? node.parentElement : node as Element;
    
    while (articleElement && articleElement !== container) {
      if (articleElement.id && articleElement.id.match(/^art\d+/i)) {
        break;
      }
      articleElement = articleElement.parentElement;
    }

    if (!articleElement || articleElement === container) {
      return 0;
    }

    // Calcula posição relativa dentro do artigo
    const articleText = articleElement.textContent || '';
    const nodeText = node.textContent || '';
    const nodeIndex = articleText.indexOf(nodeText);
    
    if (nodeIndex === -1) return 0;
    
    const absolutePosition = nodeIndex + startOffset;
    return Math.min(1, absolutePosition / articleText.length);
  } catch (error) {
    debugLog('Erro ao calcular posição relativa:', error);
    return 0;
  }
}

/**
 * Gera versão/hash da lei baseado no conteúdo
 */
export function generateLawVersion(lawContent: string): string {
  // Remove espaços em branco e normaliza para gerar hash consistente
  const normalized = lawContent
    .replace(/\s+/g, ' ')
    .trim()
    .toLowerCase();
  
  return generateTextHash(normalized);
}

/**
 * Enriquece um highlight com dados de persistência
 */
export function enrichHighlight(
  highlight: Omit<Highlight, 'id'>,
  startNode: Node,
  endNode: Node,
  container: Element,
  lawContent?: string
): Omit<Highlight, 'id'> {
  const textHash = generateTextHash(highlight.text);
  const context = extractContext(startNode, highlight.startOffset, highlight.endOffset);
  const articleContext = findArticleContext(startNode, container);
  const relativePosition = calculateRelativePosition(startNode, highlight.startOffset, container);
  const lawVersion = lawContent ? generateLawVersion(lawContent) : undefined;

  return {
    ...highlight,
    textHash,
    contextBefore: context.before,
    contextAfter: context.after,
    articleContext,
    relativePosition,
    lawVersion,
    createdAt: Date.now(),
    lastValidated: Date.now()
  };
}

/**
 * Estratégias de busca para recuperar highlights órfãos
 */
export interface SearchStrategy {
  name: string;
  search: (highlight: Highlight, container: Element) => Node | null;
}

/**
 * Busca por hash exato do texto
 */
export const exactTextHashStrategy: SearchStrategy = {
  name: 'exactTextHash',
  search: (highlight: Highlight, container: Element) => {
    if (!highlight.textHash) return null;

    const walker = document.createTreeWalker(
      container,
      NodeFilter.SHOW_TEXT,
      null
    );

    let textNode;
    while (textNode = walker.nextNode()) {
      const content = textNode.textContent || '';
      if (generateTextHash(content) === highlight.textHash) {
        return textNode;
      }
    }

    return null;
  }
};

/**
 * Busca por contexto antes e depois
 */
export const contextStrategy: SearchStrategy = {
  name: 'context',
  search: (highlight: Highlight, container: Element) => {
    if (!highlight.contextBefore && !highlight.contextAfter) return null;

    const walker = document.createTreeWalker(
      container,
      NodeFilter.SHOW_TEXT,
      null
    );

    let textNode;
    while (textNode = walker.nextNode()) {
      const content = textNode.textContent || '';

      // Verifica se o contexto antes e depois estão presentes
      if (highlight.contextBefore && content.includes(highlight.contextBefore)) {
        if (!highlight.contextAfter || content.includes(highlight.contextAfter)) {
          return textNode;
        }
      }
    }

    return null;
  }
};

/**
 * Busca por posição relativa no artigo
 */
export const relativePositionStrategy: SearchStrategy = {
  name: 'relativePosition',
  search: (highlight: Highlight, container: Element) => {
    if (!highlight.relativePosition || !highlight.articleContext) return null;

    // Encontra artigos similares
    const walker = document.createTreeWalker(
      container,
      NodeFilter.SHOW_ELEMENT,
      {
        acceptNode: (node) => {
          const element = node as Element;
          return (element.id && element.id.match(/^art\d+/i)) ?
            NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;
        }
      }
    );

    let articleElement;
    while (articleElement = walker.nextNode()) {
      const articleText = articleElement.textContent || '';
      
      // Verifica similaridade com contexto do artigo
      if (articleText.includes(highlight.articleContext.substring(0, 50))) {
        // Calcula posição aproximada baseada na posição relativa
        const targetPosition = Math.floor(articleText.length * highlight.relativePosition);
        
        // Encontra nó de texto na posição aproximada
        const textWalker = document.createTreeWalker(
          articleElement,
          NodeFilter.SHOW_TEXT,
          null
        );

        let currentPosition = 0;
        let textNode;
        while (textNode = textWalker.nextNode()) {
          const nodeLength = textNode.textContent?.length || 0;
          if (currentPosition + nodeLength >= targetPosition) {
            return textNode;
          }
          currentPosition += nodeLength;
        }
      }
    }

    return null;
  }
};

/**
 * Busca por texto exato (estratégia original)
 */
export const exactTextStrategy: SearchStrategy = {
  name: 'exactText',
  search: (highlight: Highlight, container: Element) => {
    const walker = document.createTreeWalker(
      container,
      NodeFilter.SHOW_TEXT,
      null
    );

    let textNode;
    while (textNode = walker.nextNode()) {
      const content = textNode.textContent || '';
      if (content.includes(highlight.text)) {
        return textNode;
      }
    }

    return null;
  }
};

/**
 * Busca por palavras-chave (estratégia de fallback)
 */
export const keywordStrategy: SearchStrategy = {
  name: 'keyword',
  search: (highlight: Highlight, container: Element) => {
    const words = highlight.text.split(' ').filter(w => w.length > 2);
    if (words.length === 0) return null;

    const walker = document.createTreeWalker(
      container,
      NodeFilter.SHOW_TEXT,
      null
    );

    let textNode;
    while (textNode = walker.nextNode()) {
      const content = textNode.textContent || '';
      
      // Verifica se pelo menos metade das palavras estão presentes
      const foundWords = words.filter(word => content.includes(word));
      if (foundWords.length >= Math.ceil(words.length / 2)) {
        return textNode;
      }
    }

    return null;
  }
};

/**
 * Lista de estratégias ordenadas por prioridade
 */
export const SEARCH_STRATEGIES: SearchStrategy[] = [
  exactTextHashStrategy,
  contextStrategy,
  relativePositionStrategy,
  exactTextStrategy,
  keywordStrategy
];
