require('dotenv').config();
const express = require('express');
const cors = require('cors');

// Validação da chave secreta do Stripe
const stripeSecretKey = process.env.STRIPE_SECRET_KEY;
if (!stripeSecretKey) {
  console.error('ERRO FATAL: A variável de ambiente STRIPE_SECRET_KEY não está definida.');
  console.error('Por favor, crie um arquivo .env na pasta stripebackend e adicione STRIPE_SECRET_KEY=sk_test_...');
  process.exit(1); // Encerra o processo se a chave não estiver configurada
}

const stripe = require('stripe')(stripeSecretKey);

const app = express();

// Configure CORS with specific allowed origins
const allowedOrigins = [
  'http://localhost:5173',  // Vite dev server
  'http://localhost:3000',  // Alternative dev port
  'https://your-domain.com', // Replace with your production domain
  // Add other allowed origins as needed
];

const corsOptions = {
  origin: function (origin, callback) {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);

    if (allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      console.warn(`CORS blocked request from origin: ${origin}`);
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  optionsSuccessStatus: 200
};

// Middleware
app.use(express.json());
app.use(cors(corsOptions));

app.get('/', (req, res) => {
  res.send('Backend do Stripe para GrifosApp está no ar!');
});

// Input validation helpers
function isValidUrl(string) {
  try {
    const url = new URL(string);
    return url.protocol === 'http:' || url.protocol === 'https:';
  } catch (_) {
    return false;
  }
}

function isValidPriceId(priceId) {
  // Stripe price IDs start with 'price_' and contain alphanumeric characters
  return typeof priceId === 'string' && /^price_[a-zA-Z0-9]+$/.test(priceId);
}

app.post('/create-checkout-session', async (req, res) => {
  const { priceId, successUrl, cancelUrl } = req.body;

  // Validate required parameters
  if (!priceId || !successUrl || !cancelUrl) {
    return res.status(400).json({ error: 'Parâmetros priceId, successUrl e cancelUrl são obrigatórios.' });
  }

  // Validate priceId format
  if (!isValidPriceId(priceId)) {
    return res.status(400).json({ error: 'Formato de priceId inválido.' });
  }

  // Validate URLs
  if (!isValidUrl(successUrl) || !isValidUrl(cancelUrl)) {
    return res.status(400).json({ error: 'URLs de sucesso e cancelamento devem ser válidas.' });
  }

  try {
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      mode: 'subscription',
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      success_url: successUrl,
      cancel_url: cancelUrl,
    });

    res.json({ id: session.id });
  } catch (error) {
    console.error('Erro ao criar sessão de checkout do Stripe:', error.message);
    // Retorna a mensagem de erro específica para o frontend para depuração
    res.status(500).json({ error: `Erro do Stripe: ${error.message}` });
  }
});

const PORT = process.env.PORT || 4242;
app.listen(PORT, '0.0.0.0', () => {
  console.log(`Servidor rodando na porta ${PORT} e acessível na rede`);
});
