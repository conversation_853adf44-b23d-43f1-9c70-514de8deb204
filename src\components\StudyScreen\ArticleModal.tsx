import React from 'react';
import { improveFormatting } from '../../utils';
import type { ArticleModalProps } from '../../types/lawView';

export const ArticleModal: React.FC<ArticleModalProps> = ({
  dialogArt,
  onClose,
  loadingExp,
  loadingJur,
  onExplain,
  onExplainWithExample,
  onJurisprudence,
  dialogExp,
  dialogJur,
  formatIaText,
}) => {
  if (!dialogArt) return null;

  return (
    <div className="fixed inset-0 bg-black/70 z-50 flex items-center justify-center" onClick={onClose}>
      <div
        className="modal-bg p-4 rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] flex flex-col"
        onClick={(e) => e.stopPropagation()}
      >
        <h3 className="text-lg font-bold mb-2 text-primary truncate">{dialogArt.title}</h3>
        <div 
          className="text-sm opacity-80 overflow-y-auto pr-2 flex-1" 
          dangerouslySetInnerHTML={{ __html: improveFormatting(dialogArt.texto) }} 
        />
        <div className="flex flex-wrap gap-2 mt-4 pt-2 border-t border-white/10">
          <button 
            disabled={loadingExp} 
            onClick={onExplain} 
            className="text-xs px-2 py-1 rounded bg-primary/20 hover:bg-primary/30 disabled:opacity-50"
          >
            {loadingExp ? '...' : 'Explique'}
          </button>
          <button 
            disabled={loadingExp} 
            onClick={onExplainWithExample} 
            className="text-xs px-2 py-1 rounded bg-primary/20 hover:bg-primary/30 disabled:opacity-50"
          >
            {loadingExp ? '...' : 'Com exemplo'}
          </button>
          <button 
            disabled={loadingJur} 
            onClick={onJurisprudence} 
            className="text-xs px-2 py-1 rounded bg-primary/20 hover:bg-primary/30 disabled:opacity-50"
          >
            {loadingJur ? '...' : 'Jurisprudência'}
          </button>
        </div>
        {dialogExp && (
          <div 
            className="mt-2 text-sm border-t border-dashed border-white/20 pt-2" 
            dangerouslySetInnerHTML={{ __html: formatIaText(dialogExp) }} 
          />
        )}
        {dialogJur && (
          <div 
            className="mt-2 text-sm border-t border-dashed border-white/20 pt-2" 
            dangerouslySetInnerHTML={{ __html: formatIaText(dialogJur) }} 
          />
        )}
      </div>
    </div>
  );
};
