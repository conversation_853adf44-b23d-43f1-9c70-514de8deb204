import { getNodeFromPath } from "../hooks/useTextSelection";
import type { Highlight } from "../hooks/useHighlights";
import { SEARCH_STRATEGIES } from "./highlightPersistence";

// Debug logging utility (disabled to prevent browser debug issues)
const debugLog = (message: string, ...args: any[]) => {
  // Disabled to prevent browser debug issues
  // if (import.meta.env.DEV) {
  //   console.debug(`[NodeUtils] ${message}`, ...args);
  // }
};

/**
 * Versão melhorada do getNodeFromPath que tenta recuperar de mudanças no DOM
 */
export function getNodeFromPathWithFallback(
  path: number[], 
  root: Node, 
  targetText?: string, 
  isStartNode: boolean = true
): Node | null {
  // Primeiro tenta o método padrão
  const node = getNodeFromPath(path, root);
  if (node && node.nodeType === Node.TEXT_NODE) {
    return node;
  }

  // Se falhou e temos texto alvo, tenta encontrar por busca de texto
  if (targetText && targetText.trim().length > 0) {
    debugLog(`Tentando recuperar ${isStartNode ? 'nó inicial' : 'nó final'} por busca de texto:`, targetText.substring(0, 50));

    const walker = document.createTreeWalker(
      root,
      NodeFilter.SHOW_TEXT,
      null,
      false
    );

    const searchText = targetText.trim();
    const candidates: Node[] = [];

    let textNode;
    while (textNode = walker.nextNode()) {
      if (textNode.textContent) {
        const content = textNode.textContent;

        // Para nó inicial, procura texto que começa com o início do highlight
        if (isStartNode) {
          // Verifica se o texto do nó contém o início do highlight
          if (content.includes(searchText.substring(0, Math.min(10, searchText.length)))) {
            candidates.push(textNode);
          }
        } else {
          // Para nó final, procura texto que termina com o final do highlight
          const endPart = searchText.substring(Math.max(0, searchText.length - 10));
          if (content.includes(endPart)) {
            candidates.push(textNode);
          }
        }

        // Também verifica correspondência completa
        if (content.includes(searchText)) {
          return textNode;
        }
      }
    }

    // Se encontrou candidatos, retorna o primeiro
    if (candidates.length > 0) {
      return candidates[0];
    }

    // Última tentativa: busca por qualquer parte do texto
    walker.currentNode = root;
    while (textNode = walker.nextNode()) {
      if (textNode.textContent) {
        const words = searchText.split(' ').filter(w => w.length > 2);
        for (const word of words) {
          if (textNode.textContent.includes(word)) {
            return textNode;
          }
        }
      }
    }
  }

  return null;
}

/**
 * Versão avançada que usa múltiplas estratégias para recuperar highlights órfãos
 */
export function getNodeFromPathWithAdvancedFallback(
  highlight: Highlight,
  container: Element
): Node | null {
  // Primeiro tenta o método padrão usando o path
  const node = getNodeFromPath(highlight.startContainerPath, container);
  if (node && node.nodeType === Node.TEXT_NODE) {
    // Valida se o texto ainda está correto
    const content = node.textContent || '';
    if (content.includes(highlight.text)) {
      debugLog(`Highlight ${highlight.id} encontrado via path original`);
      return node;
    }
  }

  debugLog(`Tentando recuperar highlight órfão ${highlight.id} com estratégias avançadas`);

  // Tenta cada estratégia em ordem de prioridade
  for (const strategy of SEARCH_STRATEGIES) {
    try {
      const foundNode = strategy.search(highlight, container);
      if (foundNode && foundNode.nodeType === Node.TEXT_NODE) {
        debugLog(`Highlight ${highlight.id} recuperado via estratégia: ${strategy.name}`);
        return foundNode;
      }
    } catch (error) {
      debugLog(`Estratégia ${strategy.name} falhou para highlight ${highlight.id}:`, error);
    }
  }

  debugLog(`Não foi possível recuperar highlight órfão ${highlight.id}`);
  return null;
}

/**
 * Verifica se há sobreposição entre highlights existentes
 */
export function hasOverlappingHighlight(
  startNode: Node, 
  endNode: Node, 
  startOffset: number, 
  endOffset: number, 
  container: Element
): boolean {
  try {
    // Verifica se há elementos mark entre os nós
    const range = document.createRange();
    range.setStart(startNode, startOffset);
    range.setEnd(endNode, endOffset);
    
    const fragment = range.cloneContents();
    const existingMarks = fragment.querySelectorAll('mark[data-grifo]');
    
    range.detach();
    return existingMarks.length > 0;
  } catch (error) {
    // Em caso de erro, permite a aplicação do grifo (menos restritivo)
    debugLog('Erro ao verificar sobreposição, permitindo grifo:', error);
    return false;
  }
}

/**
 * Aplica highlight de forma segura, tratando erros
 */
export function applySafeHighlight(range: Range, mark: HTMLElement): boolean {
  try {
    range.surroundContents(mark);
    return true;
  } catch (error) {
    try {
      // Fallback: extrai conteúdo e insere o mark
      const contents = range.extractContents();
      mark.appendChild(contents);
      range.insertNode(mark);
      return true;
    } catch (fallbackError) {
      console.warn('Falha ao aplicar grifo com ambos os métodos:', fallbackError);
      return false;
    }
  }
}
