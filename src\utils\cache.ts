export function cleanupOldLawCaches(maxAgeDays: number = 30): boolean {
  if (typeof window === 'undefined' || !window.localStorage) return false;

  const now = Date.now();
  const maxAgeMs = maxAgeDays * 24 * 60 * 60 * 1000;
  const prefix = 'lawCache_';
  const toDelete: string[] = [];

  try {
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (!key || !key.startsWith(prefix)) continue;

      try {
        const raw = localStorage.getItem(key);
        if (!raw) continue;
        const data = JSON.parse(raw);
        if (!data.timestamp || now - data.timestamp > maxAgeMs) {
          toDelete.push(key);
        }
      } catch {
        // If we can't parse it, it's corrupted - remove it
        toDelete.push(key);
      }
    }

    toDelete.forEach((k) => localStorage.removeItem(k));

    if (import.meta.env.DEV && toDelete.length > 0) {
      console.debug(`[cleanupOldLawCaches] Cleaned up ${toDelete.length} old cached laws`);
    }

    return toDelete.length > 0;
  } catch (error) {
    console.error('[cleanupOldLawCaches] Error during cleanup:', error);
    return false;
  }
}

/**
 * Clean up largest cached laws to free up localStorage space
 * Removes the largest laws first to maximize space freed
 */
export function cleanupLargestCachedLaws(): boolean {
  if (typeof window === 'undefined' || !window.localStorage) return false;

  const prefix = 'lawCache_';
  const cacheEntries: { key: string; size: number }[] = [];

  try {
    // Collect all cache entries with their sizes
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith(prefix)) {
        const value = localStorage.getItem(key);
        if (value) {
          cacheEntries.push({ key, size: value.length });
        }
      }
    }

    // Sort by size (largest first) and remove the largest ones
    cacheEntries.sort((a, b) => b.size - a.size);
    const toRemove = cacheEntries.slice(0, Math.ceil(cacheEntries.length / 3)); // Remove largest 1/3

    toRemove.forEach(entry => localStorage.removeItem(entry.key));

    if (import.meta.env.DEV && toRemove.length > 0) {
      console.debug(`[cleanupLargestCachedLaws] Removed ${toRemove.length} largest cached laws`);
    }

    return toRemove.length > 0;
  } catch (error) {
    console.error('[cleanupLargestCachedLaws] Error during cleanup:', error);
    return false;
  }
}
