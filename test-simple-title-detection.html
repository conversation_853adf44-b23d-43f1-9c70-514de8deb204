<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste Simples - Detecção de Títulos</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a9e;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        
        /* Estilos dos títulos */
        .combined-title {
            padding: 0.8em 1.2em !important;
            margin: 1.5em 0 !important;
            border-radius: 0.5em !important;
            font-weight: 700 !important;
            letter-spacing: 1px !important;
            color: white !important;
            text-align: center !important;
            line-height: 1.3 !important;
        }
        
        .title-livro {
            border-left-color: #ef4444 !important;
            background: rgba(127, 29, 29, 0.85) !important;
        }
        
        .title-titulo {
            border-left-color: #3b82f6 !important;
            background: rgba(30, 58, 138, 0.85) !important;
        }
        
        .title-capitulo {
            border-left-color: #10b981 !important;
            background: rgba(6, 78, 59, 0.85) !important;
        }
        
        .title-secao {
            border-left-color: #f59e0b !important;
            background: rgba(146, 64, 14, 0.85) !important;
        }
        
        .title-parte {
            border-left-color: #8b5cf6 !important;
            background: rgba(76, 29, 149, 0.85) !important;
        }
        
        .title-default {
            border-left-color: #6b7280 !important;
            background: rgba(55, 65, 81, 0.85) !important;
        }
        
        .heading {
            background: rgba(251, 191, 36, 0.08) !important;
            border-left: 4px solid #fde68a !important;
            padding: 0.5em 1em !important;
            margin-top: 2em !important;
            margin-bottom: 1.2em !important;
            border-radius: 0.4em !important;
            font-weight: 600 !important;
        }
        
        .Cap {
            background: rgba(156, 163, 175, 0.1);
            padding: 0.3em 0.8em;
            margin: 0.5em 0;
            border-radius: 0.3em;
        }
    </style>
</head>
<body>
    <h1>Teste Simples - Detecção de Títulos</h1>
    
    <div class="test-container">
        <h2>HTML de Teste (Baseado nas Imagens)</h2>
        <div id="lei-container">
            <p class="heading">LIVRO PRIMEIRO</p>
            <p class="Cap">Das Pessoas</p>
            <p class="heading">TÍTULO I</p>
            <p class="Cap">Das Pessoas Naturais</p>
            <p class="heading">CAPÍTULO I</p>
            <p class="Cap">Da Personalidade e da Capacidade</p>
            <p class="heading">SEÇÃO I</p>
            <p class="Cap">Da Personalidade</p>
        </div>
        
        <button onclick="analyzeCurrentState()">Analisar Estado Atual</button>
        <button onclick="processWithLogs()">Processar com Logs Detalhados</button>
        <button onclick="resetTest()">Resetar Teste</button>
        
        <div id="result" class="result info" style="display: none;"></div>
    </div>

    <script>
        function showResult(message) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.style.display = 'block';
        }

        function analyzeCurrentState() {
            const container = document.getElementById('lei-container');
            const paragraphs = container.querySelectorAll('p');
            
            let analysis = 'ANÁLISE DO ESTADO ATUAL:\n\n';
            
            paragraphs.forEach((p, index) => {
                const text = p.textContent?.trim() || '';
                const classes = p.className;
                analysis += `Parágrafo ${index + 1}:\n`;
                analysis += `  Texto: "${text}"\n`;
                analysis += `  Classes: "${classes}"\n`;
                analysis += `  Tem combined-title: ${p.classList.contains('combined-title')}\n\n`;
            });
            
            const combinedTitles = container.querySelectorAll('.combined-title');
            analysis += `\nTítulos com combined-title: ${combinedTitles.length}\n`;
            
            showResult(analysis);
        }

        function processWithLogs() {
            const container = document.getElementById('lei-container');
            let logs = 'PROCESSAMENTO COM LOGS:\n\n';
            
            // Simular a função combineTitlesInDOM com logs detalhados
            const paragraphs = Array.from(container.querySelectorAll('p'));
            logs += `📊 Found ${paragraphs.length} paragraphs to analyze\n\n`;
            
            // Função getTitleType
            const getTitleType = (text) => {
                const upperText = text.toUpperCase();
                if (upperText.includes('LIVRO')) return 'title-livro';
                if (upperText.includes('TÍTULO') || upperText.includes('TITULO')) return 'title-titulo';
                if (upperText.includes('CAPÍTULO') || upperText.includes('CAPITULO')) return 'title-capitulo';
                if (upperText.includes('SEÇÃO') || upperText.includes('SECAO')) return 'title-secao';
                if (upperText.includes('PARTE')) return 'title-parte';
                return 'title-default';
            };
            
            const patterns = {
                processedTitlePattern: /^(LIVRO|PARTE|T[ÍI]TULO|CAP[ÍI]TULO|SE[ÇC][ÃA]O)\s+/i
            };
            
            // Primeiro passo: processar títulos existentes
            paragraphs.forEach(p => {
                const text = p.textContent?.trim() || '';
                if (patterns.processedTitlePattern.test(text) &&
                    !p.classList.contains('combined-title') &&
                    (p.classList.contains('heading') || p.classList.contains('livro') ||
                     p.classList.contains('titulo') || p.classList.contains('capitulo') || p.classList.contains('secao'))) {
                    const titleType = getTitleType(text);
                    logs += `🎨 Adding color class to existing title: "${text}" -> ${titleType}\n`;
                    p.classList.add('combined-title', titleType);
                    p.innerHTML = text.toUpperCase();
                }
            });
            
            // Segundo passo: combinar títulos
            const toRemove = [];
            for (let i = 0; i < paragraphs.length - 1; i++) {
                const current = paragraphs[i];
                const next = paragraphs[i + 1];
                
                if (current.classList.contains('combined-title') || toRemove.includes(current)) continue;
                
                const currentText = current.textContent?.trim() || '';
                const nextText = next.textContent?.trim() || '';
                
                // Verificar padrão heading + Cap
                if (current.classList.contains('heading') &&
                    patterns.processedTitlePattern.test(currentText) &&
                    next.classList.contains('Cap') &&
                    nextText && nextText.length > 3) {
                    
                    const combinedTitle = `${currentText.toUpperCase()} - ${nextText.toUpperCase()}`;
                    const titleType = getTitleType(currentText);
                    logs += `🎯 Heading + Cap combination: "${currentText}" + "${nextText}" -> ${titleType}\n`;
                    current.innerHTML = combinedTitle;
                    current.className = `combined-title ${titleType}`;
                    toRemove.push(next);
                }
            }
            
            // Remover elementos
            toRemove.forEach(element => element.remove());
            logs += `🗑️ Removed ${toRemove.length} elements\n\n`;
            
            // Resultado final
            const processedTitles = container.querySelectorAll('.combined-title');
            logs += `✅ Finished processing. ${processedTitles.length} titles now have combined-title class\n\n`;
            
            processedTitles.forEach((title, index) => {
                logs += `📝 Title ${index + 1}: "${title.textContent}" -> classes: ${title.className}\n`;
            });
            
            showResult(logs);
        }

        function resetTest() {
            const container = document.getElementById('lei-container');
            container.innerHTML = `
                <p class="heading">LIVRO PRIMEIRO</p>
                <p class="Cap">Das Pessoas</p>
                <p class="heading">TÍTULO I</p>
                <p class="Cap">Das Pessoas Naturais</p>
                <p class="heading">CAPÍTULO I</p>
                <p class="Cap">Da Personalidade e da Capacidade</p>
                <p class="heading">SEÇÃO I</p>
                <p class="Cap">Da Personalidade</p>
            `;
            showResult('Teste resetado para o estado inicial.');
        }
    </script>
</body>
</html>
