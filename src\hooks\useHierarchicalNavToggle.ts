import { useState, useEffect } from 'react';

const STORAGE_KEY = 'hierarchical_nav_visible';

export function useHierarchicalNavToggle() {
  const [isVisible, setIsVisible] = useState(() => {
    // Default to true, but check localStorage
    if (typeof localStorage !== 'undefined') {
      const stored = localStorage.getItem(STORAGE_KEY);
      return stored !== null ? stored === 'true' : true;
    }
    return true;
  });

  const toggle = () => {
    setIsVisible(prev => {
      const newValue = !prev;
      if (typeof localStorage !== 'undefined') {
        localStorage.setItem(STORAGE_KEY, String(newValue));
      }
      return newValue;
    });
  };

  return { isVisible, toggle };
}
