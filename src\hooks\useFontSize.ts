import { useState, useEffect, useCallback } from 'react';

const MIN_FONT_SIZE = 12;
const MAX_FONT_SIZE = 24;
const FONT_SIZE_STEP = 2;

export function useFontSize(ref: React.RefObject<HTMLElement>) {
  const [fontSize, setFontSize] = useState<number>(() => {
    if (typeof window === 'undefined') return 16;
    const stored = localStorage.getItem('fontSize');
    return stored ? parseInt(stored, 10) : 16;
  });

  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('fontSize', String(fontSize));
    }
    if (ref.current) {
      ref.current.style.setProperty('--fs', `${fontSize}px`);
      ref.current.style.fontSize = `${fontSize}px`;
    }
  }, [fontSize, ref]);

  const aumentar = useCallback(() => {
    setFontSize((f) => Math.min(f + FONT_SIZE_STEP, MAX_FONT_SIZE));
  }, []);

  const diminuir = useCallback(() => {
    setFontSize((f) => Math.max(f - FONT_SIZE_STEP, MIN_FONT_SIZE));
  }, []);

  return { fontSize, aumentar, diminuir };
}
