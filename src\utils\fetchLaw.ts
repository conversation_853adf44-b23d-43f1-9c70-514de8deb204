import { BACKEND_URL } from "./env";
import { fetchLocalLawHtml } from "./localLaw";
import { fetchJson } from "./http";
import { cleanupOldLawCaches, cleanupLargestCachedLaws } from "./cache";
import { generateLawVersion } from "./highlightPersistence";

// Utility to reduce console noise in development mode (disabled to prevent browser debug issues)
const isDev = import.meta.env.DEV;
const devLog = {
  debug: (message: string, ...args: any[]) => {
    // Disabled to prevent browser debug issues
    // if (isDev) {
    //   console.debug(message, ...args);
    // }
  },
  warn: (message: string, ...args: any[]) => {
    // Only show warnings in production
    if (!isDev) {
      console.warn(message, ...args);
    }
  }
};

export interface FetchLawResult {
  html: string;
  source: "local" | "remote" | "localfile";
  version?: string; // Hash da versão da lei
  isUpdated?: boolean; // Se a lei foi atualizada desde o cache
}

/**
 * Simplified Law Loading Flow:
 * 1. Check localStorage cache for the law content
 * 2. If not cached, call the webscraper API at http://localhost:3005 to fetch the law
 * 3. Cache the fetched content in localStorage for future use
 *
 * Note: Firebase/Firestore law storage has been removed. Laws are only cached locally.
 * Firebase is still used for user authentication and highlight storage.
 */
export async function fetchLawHtml(
  leiId: string,
  urlOriginal: string,
  signal?: AbortSignal
): Promise<FetchLawResult> {
  // Step 1: Check localStorage cache for the law content
  const cached = getCachedLaw(urlOriginal);
  if (cached && cached.html && typeof cached.html === 'string' && cached.html.trim().length > 0) {
    console.debug(`[fetchLawHtml] Found cached law for ${leiId}`);
    const version = cached.version || generateLawVersion(cached.html);
    return {
      html: cached.html,
      source: "local",
      version,
      isUpdated: false
    };
  }

  // Step 2: Firestore law storage/retrieval removed - laws are no longer stored in Firebase

  // Step 2: Try to fetch local law file (/laws/ID.html) - but skip placeholders
  // Only try for laws that are known to have local files to avoid unnecessary 404s
  const knownLocalLaws = ['constituicao-federal', 'clt'];
  if (knownLocalLaws.includes(leiId)) {
    const localHtml = await fetchLocalLawHtml(leiId, signal);
    if (localHtml) {
      const version = generateLawVersion(localHtml);
      setCachedLaw(urlOriginal, { html: localHtml, source: 'localfile', version });
      return {
        html: localHtml,
        source: 'localfile',
        version,
        isUpdated: false
      };
    }
  }

  // Step 3: Call the webscraper API at http://localhost:3005 to fetch the law
  const backendUrl = `${BACKEND_URL}/laws?url=${encodeURIComponent(urlOriginal)}`;
  console.debug(`[fetchLawHtml] Fetching from webscraper API: ${backendUrl}`);

  try {
    const data = await fetchJson<{ html: string }>(backendUrl, {
      signal,
      timeout: 15000,
    });
    const { html } = data;

    // Validate that we got valid HTML content
    if (!html || typeof html !== 'string' || html.trim().length === 0) {
      throw new Error(`Servidor retornou conteúdo inválido para a lei ${leiId}`);
    }

    console.debug(`[fetchLawHtml] Successfully fetched ${leiId} from webscraper API`);

    // Generate version hash for the new content
    const version = generateLawVersion(html);

    // Check if this is an update by comparing with cached version
    let isUpdated = false;
    if (cached && cached.version) {
      isUpdated = cached.version !== version;
      if (isUpdated) {
        console.debug(`[fetchLawHtml] Law ${leiId} was updated (version changed)`);
      }
    }

    // Step 4: Cache the fetched content in localStorage for future use
    // Note: setCachedLaw now handles quota errors gracefully and won't throw
    setCachedLaw(urlOriginal, { html, source: "remote", version });

    return {
      html,
      source: "remote",
      version,
      isUpdated
    };
  } catch (err: any) {
    // Handle AbortError gracefully - this is expected in development mode
    if (err.name === "AbortError" || err.message?.includes("aborted")) {
      devLog.debug(`[fetchLawHtml] Request aborted for ${leiId} (this is normal in development)`);
      throw err; // Still throw so the calling code can handle it
    }

    console.error(`[fetchLawHtml] Webscraper API failed for ${leiId}:`, err);
    throw err;
  }
}

// Funções auxiliares para cache local de códigos
export function getCachedLaw(
  urlOriginal: string
): { html: string; source: string; timestamp: number; version?: string } | null {
  const CACHE_PREFIX = "lawCache_";
  const cacheKey = CACHE_PREFIX + encodeURIComponent(urlOriginal);
  if (typeof window !== "undefined") {
    const cached = localStorage.getItem(cacheKey);
    if (cached) {
      try {
        return JSON.parse(cached);
      } catch {}
    }
  }
  return null;
}

export function setCachedLaw(
  urlOriginal: string,
  data: { html: string; source: string; version?: string }
) {
  const CACHE_PREFIX = "lawCache_";
  const cacheKey = CACHE_PREFIX + encodeURIComponent(urlOriginal);

  if (typeof window === "undefined") return;

  const dataToCache = { ...data, timestamp: Date.now() };
  const jsonData = JSON.stringify(dataToCache);

  try {
    localStorage.setItem(cacheKey, jsonData);
    devLog.debug(`[setCachedLaw] Successfully cached law: ${urlOriginal}`);
  } catch (error: any) {
    devLog.warn(`[setCachedLaw] Failed to cache law ${urlOriginal}:`, error.message);

    if (error.name === 'QuotaExceededError' || error.message?.includes('quota')) {
      devLog.debug('[setCachedLaw] localStorage quota exceeded, attempting cleanup...');

      // Try to free up space by removing old cached laws
      const success = cleanupOldLawCaches(7) || cleanupLargestCachedLaws();

      if (success) {
        try {
          // Try again after cleanup
          localStorage.setItem(cacheKey, jsonData);
          console.debug(`[setCachedLaw] Successfully cached law after cleanup: ${urlOriginal}`);
        } catch (retryError) {
          devLog.debug('[setCachedLaw] Still failed after cleanup, skipping cache for this law');
          // Don't throw - law loading should continue without caching
        }
      } else {
        devLog.debug('[setCachedLaw] Cleanup failed, skipping cache for this law');
        // Don't throw - law loading should continue without caching
      }
    }
  }
}
