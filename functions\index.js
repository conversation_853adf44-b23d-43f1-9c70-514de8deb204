const functions = require("firebase-functions");
const admin = require("firebase-admin");

admin.initializeApp();

/**
 * Cria uma sessão de checkout do Stripe.
 * @param {object} data Dados enviados pelo cliente.
 * @param {string} data.priceId O ID do preço do produto no Stripe.
 * @param {string} data.successUrl A URL para redirecionar em caso de sucesso.
 * @param {string} data.cancelUrl A URL para redirecionar em caso de cancelamento.
 * @param {object} context Informações de autenticação do usuário.
 * @return {Promise<{id: string}>} Retorna o ID da sessão de checkout.
 */
exports.createCheckoutSession = functions.https.onCall(async (data, context) => {
  // Inicializa o Stripe dentro da função para garantir que a config foi carregada.
  const stripeSecret = functions.config().stripe.secret;
  if (!stripeSecret) {
    throw new functions.https.HttpsError(
        "failed-precondition",
        "A chave secreta do Stripe não está configurada no ambiente do Firebase.",
    );
  }
  const stripe = require("stripe")(stripeSecret);

  // Verifica se o usuário está autenticado.
  if (!context.auth) {
    throw new functions.https.HttpsError(
        "unauthenticated",
        "Você precisa estar logado para fazer uma assinatura.",
    );
  }

  const {priceId, successUrl, cancelUrl} = data;

  if (!priceId || !successUrl || !cancelUrl) {
    throw new functions.https.HttpsError(
        "invalid-argument",
        "A função precisa dos parâmetros 'priceId', 'successUrl' e 'cancelUrl'.",
    );
  }

  try {
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ["card"],
      mode: "subscription",
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      // Associa a sessão de checkout ao usuário do Firebase.
      client_reference_id: context.auth.uid,
      success_url: successUrl,
      cancel_url: cancelUrl,
    });

    return {id: session.id};
  } catch (error) {
    console.error("Erro ao criar sessão de checkout do Stripe:", error);
    throw new functions.https.HttpsError(
        "internal",
        "Não foi possível criar a sessão de checkout.",
    );
  }
});

