import { Search } from "lucide-react";
import { ChangeEvent } from "react";

interface Props {
  query: string;
  setQuery: (q: string) => void;
}

export function SearchBar({ query, setQuery }: Props) {
  function onChange(e: ChangeEvent<HTMLInputElement>) {
    setQuery(e.target.value);
  }
  return (
    <div className="relative w-full max-w-md mx-auto">
      <Search className="absolute left-3 top-1/2 -translate-y-1/2 opacity-60" size={18} />
      <input
        type="text"
        value={query}
        onChange={onChange}
        placeholder="Buscar lei..."
        className="w-full pl-10 pr-4 py-2 rounded-md bg-white/10 focus:outline-none focus:ring-2 focus:ring-primary"
      />
    </div>
  );
} 