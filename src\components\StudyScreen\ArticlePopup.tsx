import React from 'react';
import type { Article as ArticleType } from '../../types';

interface ArticlePopupProps {
  article: ArticleType;
  position: { top: number; left: number };
  onClose: () => void;
}

export function ArticlePopup({ article, position, onClose }: ArticlePopupProps) {
  return (
    <div
      className="fixed z-50 modal-bg rounded-lg shadow-xl p-4 w-[90vw] max-w-md animate-scaleIn"
      style={{ top: position.top, left: position.left }}
    >
      <button onClick={onClose} className="absolute top-2 right-3 text-red-500 text-lg font-bold">✕</button>
      <h3 className="text-lg font-bold mb-2">{article.id}</h3>
      <div
        className="prose prose-sm dark:prose-invert max-h-[50vh] overflow-y-auto pr-4"
        dangerouslySetInnerHTML={{ __html: article.texto }}
      />
    </div>
  );
}
