<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste Final - Verificação Completa</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .combined-title {
            padding: 0.7rem 1rem !important;
            margin: 1.5rem 0 1rem 0 !important;
            border-radius: 4px !important;
            font-weight: 600 !important;
            font-size: 0.95em !important;
            letter-spacing: 0.3px !important;
            text-transform: uppercase !important;
            color: #f1f5f9 !important;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12) !important;
            text-align: left !important;
            line-height: 1.3 !important;
            border-left: 3px solid !important;
        }
        
        .title-capitulo {
            border-left-color: #10b981 !important;
            background: rgba(6, 78, 59, 0.85) !important;
        }
        
        .title-secao {
            border-left-color: #f59e0b !important;
            background: rgba(146, 64, 14, 0.85) !important;
        }
        
        .heading {
            background: rgba(251, 191, 36, 0.08) !important;
            border-left: 4px solid #fde68a !important;
            padding: 0.5em 1em !important;
            margin-top: 2em !important;
            margin-bottom: 1.2em !important;
            border-radius: 0.4em !important;
            font-weight: 600 !important;
        }
        
        .success { color: #10b981; font-weight: bold; }
        .error { color: #dc2626; font-weight: bold; }
        .warning { color: #f59e0b; font-weight: bold; }
    </style>
</head>
<body>
    <h1>🔍 Teste Final - Verificação Completa</h1>
    
    <div class="container">
        <h2>Status da Correção</h2>
        <p><strong>Problema:</strong> "Alegação" e "Da Produção" apareciam como títulos individuais coloridos</p>
        <p><strong>Solução:</strong> Padrão `processedTitlePattern` corrigido para incluir apenas títulos principais</p>
        
        <div id="test-container">
            <!-- Estrutura de teste -->
            <p class="heading">CAPÍTULO I</p>
            <p>Da Personalidade e da Capacidade</p>
            
            <p class="heading">SEÇÃO I</p>
            <p>Das Regras Gerais</p>
            
            <p class="heading">Subseção I</p>
            <p>Da Produção da Prova Testemunhal</p>
            
            <!-- ESTES NÃO DEVEM VIRAR TÍTULOS INDIVIDUAIS -->
            <p class="heading">Alegação</p>
            <p>Das Regras Gerais</p>
            
            <p class="heading">Da Produção</p>
            <p>Dos Procedimentos Específicos</p>
            
            <!-- Texto normal -->
            <p>Art. 1º Este é um artigo normal.</p>
            <p>§ 1º Este é um parágrafo normal.</p>
        </div>
        
        <button onclick="testarCorrecao()">🧪 Testar Correção</button>
        <button onclick="resetTest()">🔄 Resetar</button>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        function showResult(message) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.style.display = 'block';
        }

        function testarCorrecao() {
            const container = document.getElementById('test-container');
            
            // PADRÃO CORRIGIDO (exato do código)
            const processedTitlePattern = /^(LIVRO|PARTE|T[ÍI]TULO|CAP[ÍI]TULO|SE[ÇC][ÃA]O|SUBSE[ÇC][ÃA]O)\s+/i;
            
            let logs = '🔍 TESTE FINAL DA CORREÇÃO\n';
            logs += '=' .repeat(50) + '\n\n';
            
            // Testar o padrão corrigido
            logs += '📋 TESTE DO PADRÃO CORRIGIDO:\n';
            logs += `Padrão: ${processedTitlePattern}\n\n`;
            
            const testCases = [
                'CAPÍTULO I',
                'SEÇÃO I', 
                'Subseção I',
                'SUBSEÇÃO I',
                'Alegação',
                'Da Produção',
                'Das Regras',
                'Do Cumprimento',
                'Art. 1º'
            ];
            
            testCases.forEach(text => {
                const matches = processedTitlePattern.test(text);
                const status = matches ? '✅ MATCH' : '❌ NO MATCH';
                const expected = ['CAPÍTULO I', 'SEÇÃO I', 'Subseção I', 'SUBSEÇÃO I'].includes(text) ? '✅' : '❌';
                const correct = (matches && expected === '✅') || (!matches && expected === '❌');
                
                logs += `"${text}": ${status} ${correct ? '✅ CORRETO' : '⚠️ INCORRETO'}\n`;
            });
            
            logs += '\n' + '=' .repeat(50) + '\n';
            logs += '🎯 VERIFICAÇÃO ESPECÍFICA:\n\n';
            
            // Verificar casos específicos
            const alegacaoMatch = processedTitlePattern.test('Alegação');
            const daProducaoMatch = processedTitlePattern.test('Da Produção');
            const capituloMatch = processedTitlePattern.test('CAPÍTULO I');
            const secaoMatch = processedTitlePattern.test('SEÇÃO I');
            
            logs += `❌ "Alegação" deve ser título: ${alegacaoMatch ? 'SIM (PROBLEMA!)' : 'NÃO (CORRETO!)'}\n`;
            logs += `❌ "Da Produção" deve ser título: ${daProducaoMatch ? 'SIM (PROBLEMA!)' : 'NÃO (CORRETO!)'}\n`;
            logs += `✅ "CAPÍTULO I" deve ser título: ${capituloMatch ? 'SIM (CORRETO!)' : 'NÃO (PROBLEMA!)'}\n`;
            logs += `✅ "SEÇÃO I" deve ser título: ${secaoMatch ? 'SIM (CORRETO!)' : 'NÃO (PROBLEMA!)'}\n`;
            
            logs += '\n' + '=' .repeat(50) + '\n';
            
            // Resultado final
            const problemsFixed = !alegacaoMatch && !daProducaoMatch;
            const mainTitlesWork = capituloMatch && secaoMatch;
            
            if (problemsFixed && mainTitlesWork) {
                logs += '🎉 CORREÇÃO FUNCIONOU PERFEITAMENTE!\n';
                logs += '✅ Títulos problemáticos não são mais detectados\n';
                logs += '✅ Títulos principais continuam funcionando\n';
                logs += '✅ Padrão corrigido está funcionando como esperado\n';
            } else if (problemsFixed && !mainTitlesWork) {
                logs += '⚠️ CORREÇÃO PARCIAL\n';
                logs += '✅ Títulos problemáticos foram corrigidos\n';
                logs += '❌ Alguns títulos principais não estão funcionando\n';
            } else if (!problemsFixed && mainTitlesWork) {
                logs += '❌ PROBLEMA AINDA EXISTE\n';
                logs += '❌ Títulos problemáticos ainda são detectados\n';
                logs += '✅ Títulos principais funcionam\n';
            } else {
                logs += '🚨 MÚLTIPLOS PROBLEMAS\n';
                logs += '❌ Títulos problemáticos ainda são detectados\n';
                logs += '❌ Títulos principais não funcionam\n';
            }
            
            logs += '\n' + '=' .repeat(50) + '\n';
            logs += '📊 RESUMO:\n';
            logs += `Padrão problemático "Alegação": ${alegacaoMatch ? '❌ AINDA DETECTA' : '✅ CORRIGIDO'}\n`;
            logs += `Padrão problemático "Da Produção": ${daProducaoMatch ? '❌ AINDA DETECTA' : '✅ CORRIGIDO'}\n`;
            logs += `Funcionalidade "CAPÍTULO": ${capituloMatch ? '✅ FUNCIONA' : '❌ QUEBRADO'}\n`;
            logs += `Funcionalidade "SEÇÃO": ${secaoMatch ? '✅ FUNCIONA' : '❌ QUEBRADO'}\n`;
            
            showResult(logs);
        }

        function resetTest() {
            const container = document.getElementById('test-container');
            container.innerHTML = `
                <p class="heading">CAPÍTULO I</p>
                <p>Da Personalidade e da Capacidade</p>
                
                <p class="heading">SEÇÃO I</p>
                <p>Das Regras Gerais</p>
                
                <p class="heading">Subseção I</p>
                <p>Da Produção da Prova Testemunhal</p>
                
                <p class="heading">Alegação</p>
                <p>Das Regras Gerais</p>
                
                <p class="heading">Da Produção</p>
                <p>Dos Procedimentos Específicos</p>
                
                <p>Art. 1º Este é um artigo normal.</p>
                <p>§ 1º Este é um parágrafo normal.</p>
            `;
            showResult('Teste resetado para o estado inicial.');
        }
    </script>
</body>
</html>
