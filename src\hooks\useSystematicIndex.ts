import { useState, useEffect } from 'react';

export interface SystematicIndexItem {
  id: string;
  title: string;
  type: 'livro' | 'titulo' | 'capitulo' | 'secao' | 'subsecao';
  level: number;
  children?: SystematicIndexItem[];
  element?: HTMLElement;
  articleCount?: number; // Contador de artigos nesta seção
}

export interface SystematicIndexStructure {
  items: SystematicIndexItem[];
  totalItems: number;
}

/**
 * Hook para extrair a estrutura hierárquica completa de uma lei
 * Analisa o HTML e constrói um índice sistemático com todos os níveis
 */
export function useSystematicIndex(htmlRemoto: string | null): SystematicIndexStructure {
  const [structure, setStructure] = useState<SystematicIndexStructure>({
    items: [],
    totalItems: 0
  });

  useEffect(() => {
    if (!htmlRemoto) {
      setStructure({ items: [], totalItems: 0 });
      return;
    }

    // Criar um elemento temporário para parsing
    const temp = document.createElement('div');
    temp.innerHTML = htmlRemoto;

    const items: SystematicIndexItem[] = [];
    let totalItems = 0;

    // Função para detectar tipo de elemento hierárquico
    const detectHierarchyType = (text: string, element: Element): { type: string; match: RegExpMatchArray | null; fullTitle: string } | null => {
      // Limpar texto
      const cleanText = text.replace(/\s+/g, ' ').trim();

      // Padrões principais - removido 'artigo'
      const patterns = [
        { type: 'livro', regex: /^(LIVRO|PARTE)\s+([IVX\d]+)(.*)$/i },
        { type: 'titulo', regex: /^T[ÍI]TULO\s+([IVX\d]+|ÚNICO)(.*)$/i },
        { type: 'capitulo', regex: /^CAP[ÍI]TULO\s+([IVX\d]+)(.*)$/i },
        { type: 'secao', regex: /^SE[ÇC][ÃA]O\s+([IVX\d]+)(.*)$/i },
        { type: 'subsecao', regex: /^SUBSE[ÇC][ÃA]O\s+([IVX\d]+)(.*)$/i }
      ];

      // Padrões alternativos
      const alternativePatterns = [
        { type: 'livro', regex: /^(LIVRO|PARTE)\s*[-–—]\s*(.*)$/i },
        { type: 'titulo', regex: /^T[ÍI]TULO\s*[-–—]\s*(.*)$/i },
        { type: 'capitulo', regex: /^CAP[ÍI]TULO\s*[-–—]\s*(.*)$/i },
        { type: 'secao', regex: /^SE[ÇC][ÃA]O\s*[-–—]\s*(.*)$/i },
        { type: 'subsecao', regex: /^SUBSE[ÇC][ÃA]O\s*[-–—]\s*(.*)$/i }
      ];

      // Verificar padrões principais
      for (const pattern of patterns) {
        const match = cleanText.match(pattern.regex);
        if (match) {
          // Capturar o texto completo, incluindo descrição
          let fullTitle = cleanText;

          // Se há uma descrição após o número/identificador
          if (match[3] && match[3].trim()) {
            const description = match[3].trim().replace(/^[-–—\s]+/, '');
            if (description) {
              fullTitle = `${match[1]} ${match[2]} - ${description}`;
            }
          }

          return { type: pattern.type, match, fullTitle };
        }
      }

      // Verificar padrões alternativos
      for (const pattern of alternativePatterns) {
        const match = cleanText.match(pattern.regex);
        if (match) {
          let fullTitle = cleanText;

          // Se há uma descrição após o separador
          if (match[2] && match[2].trim()) {
            fullTitle = `${match[1]} - ${match[2].trim()}`;
          }

          return { type: pattern.type, match, fullTitle };
        }
      }

      // Verificar por classes CSS específicas
      if (element.classList) {
        if (element.classList.contains('heading-livro') || element.classList.contains('L')) {
          return { type: 'livro', match: [text], fullTitle: text };
        }
        if (element.classList.contains('heading-titulo') || element.classList.contains('T')) {
          return { type: 'titulo', match: [text], fullTitle: text };
        }
        if (element.classList.contains('heading-capitulo') || element.classList.contains('C')) {
          return { type: 'capitulo', match: [text], fullTitle: text };
        }
        if (element.classList.contains('heading-secao') || element.classList.contains('S')) {
          return { type: 'secao', match: [text], fullTitle: text };
        }
        if (element.classList.contains('subsecao') || element.classList.contains('SS')) {
          return { type: 'subsecao', match: [text], fullTitle: text };
        }
      }

      return null;
    };

    // Função para obter texto expandido (incluindo elementos adjacentes)
    const getExpandedText = (element: Element): string => {
      let text = element.textContent?.trim() || '';

      // Se o texto é muito curto, tentar capturar de elementos adjacentes
      if (text.length < 50) {
        const nextSibling = element.nextElementSibling;
        if (nextSibling && nextSibling.textContent) {
          const nextText = nextSibling.textContent.trim();
          // Se o próximo elemento parece ser uma continuação
          if (nextText && !nextText.match(/^(LIVRO|TÍTULO|CAPÍTULO|SEÇÃO|Art\.)/i)) {
            text += ' ' + nextText;
          }
        }
      }

      return text.replace(/\s+/g, ' ').trim();
    };

    // Buscar todos os elementos potenciais, incluindo elementos com classes específicas
    const allElements = Array.from(temp.querySelectorAll(`
      p, div, h1, h2, h3, h4, h5, h6, strong,
      [class*="heading"], [id^="art-"],
      .L, .T, .C, .S, .SS,
      [class*="livro"], [class*="titulo"], [class*="capitulo"], [class*="secao"],
      [style*="font-weight"], [style*="bold"],
      .center, .centered, [align="center"]
    `));
    
    // Stack para manter a hierarquia
    const hierarchyStack: SystematicIndexItem[] = [];

    // Primeiro, vamos contar os artigos por seção
    const articleCounts = new Map<string, number>();
    let currentSectionId = '';

    allElements.forEach((element, index) => {
      const text = getExpandedText(element);
      if (!text) return;

      // Verificar se é um artigo para contagem
      if (element.id && element.id.startsWith('art-')) {
        const articleMatch = text.match(/^Art\.?\s*(\d+)/i);
        if (articleMatch && currentSectionId) {
          articleCounts.set(currentSectionId, (articleCounts.get(currentSectionId) || 0) + 1);
        }
      } else {
        // Verificar se é uma seção hierárquica
        const detection = detectHierarchyType(text, element);
        if (detection) {
          currentSectionId = `${detection.type}-${index}`;
        }
      }
    });

    // Agora processar apenas elementos hierárquicos (sem artigos)
    allElements.forEach((element, index) => {
      const text = getExpandedText(element);
      if (!text) return;

      let item: SystematicIndexItem | null = null;

      // Pular artigos - não incluir na hierarquia
      if (element.id && element.id.startsWith('art-')) {
        return;
      }

      // Verificar tipos hierárquicos
      const detection = detectHierarchyType(text, element);
      if (detection) {
        const levelMap = {
          livro: 0,
          titulo: 1,
          capitulo: 2,
          secao: 3,
          subsecao: 4
        };

        const itemId = `${detection.type}-${index}`;
        item = {
          id: itemId,
          title: detection.fullTitle || text,
          type: detection.type as any,
          level: levelMap[detection.type as keyof typeof levelMap],
          children: [],
          articleCount: articleCounts.get(itemId) || 0,
          element: element as HTMLElement
        };
      }

      if (item) {
        totalItems++;

        // Ajustar a hierarquia
        while (hierarchyStack.length > 0 && hierarchyStack[hierarchyStack.length - 1].level >= item.level) {
          hierarchyStack.pop();
        }

        // Adicionar ao pai apropriado ou à raiz
        if (hierarchyStack.length > 0) {
          const parent = hierarchyStack[hierarchyStack.length - 1];
          if (!parent.children) parent.children = [];
          parent.children.push(item);
        } else {
          items.push(item);
        }

        // Adicionar à stack se não for artigo (artigos não têm filhos)
        if (item.type !== 'artigo') {
          hierarchyStack.push(item);
        }
      }
    });

    setStructure({ items, totalItems });
  }, [htmlRemoto]);

  return structure;
}
