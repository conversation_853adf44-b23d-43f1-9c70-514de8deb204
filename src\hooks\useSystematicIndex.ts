import { useState, useEffect } from 'react';

export interface SystematicIndexItem {
  id: string;
  title: string;
  type: 'livro' | 'titulo' | 'capitulo' | 'secao' | 'subsecao' | 'artigo';
  level: number;
  children?: SystematicIndexItem[];
  element?: HTMLElement;
}

export interface SystematicIndexStructure {
  items: SystematicIndexItem[];
  totalItems: number;
}

/**
 * Hook para extrair a estrutura hierárquica completa de uma lei
 * Analisa o HTML e constrói um índice sistemático com todos os níveis
 */
export function useSystematicIndex(htmlRemoto: string | null): SystematicIndexStructure {
  const [structure, setStructure] = useState<SystematicIndexStructure>({
    items: [],
    totalItems: 0
  });

  useEffect(() => {
    if (!htmlRemoto) {
      setStructure({ items: [], totalItems: 0 });
      return;
    }

    // Criar um elemento temporário para parsing
    const temp = document.createElement('div');
    temp.innerHTML = htmlRemoto;

    const items: SystematicIndexItem[] = [];
    let totalItems = 0;

    // Função para detectar tipo de elemento hierárquico
    const detectHierarchyType = (text: string, element: Element): { type: string; match: RegExpMatchArray | null } | null => {
      // Padrões principais
      const patterns = [
        { type: 'livro', regex: /^(LIVRO|PARTE)\s+([IVX\d]+)/i },
        { type: 'titulo', regex: /^T[ÍI]TULO\s+([IVX\d]+)/i },
        { type: 'capitulo', regex: /^CAP[ÍI]TULO\s+([IVX\d]+)/i },
        { type: 'secao', regex: /^SE[ÇC][ÃA]O\s+([IVX\d]+)/i },
        { type: 'subsecao', regex: /^SUBSE[ÇC][ÃA]O\s+([IVX\d]+)/i },
        { type: 'artigo', regex: /^Art\.?\s*(\d+)/i }
      ];

      // Padrões alternativos
      const alternativePatterns = [
        { type: 'livro', regex: /^(LIVRO|PARTE)\s*[-–—]\s*/i },
        { type: 'titulo', regex: /^T[ÍI]TULO\s*[-–—]\s*/i },
        { type: 'capitulo', regex: /^CAP[ÍI]TULO\s*[-–—]\s*/i },
        { type: 'secao', regex: /^SE[ÇC][ÃA]O\s*[-–—]\s*/i },
        { type: 'subsecao', regex: /^SUBSE[ÇC][ÃA]O\s*[-–—]\s*/i }
      ];

      // Verificar padrões principais
      for (const pattern of patterns) {
        const match = text.match(pattern.regex);
        if (match) {
          return { type: pattern.type, match };
        }
      }

      // Verificar padrões alternativos
      for (const pattern of alternativePatterns) {
        const match = text.match(pattern.regex);
        if (match) {
          return { type: pattern.type, match };
        }
      }

      // Verificar por classes CSS específicas
      if (element.classList) {
        if (element.classList.contains('heading-livro') || element.classList.contains('L')) {
          return { type: 'livro', match: [text] };
        }
        if (element.classList.contains('heading-titulo') || element.classList.contains('T')) {
          return { type: 'titulo', match: [text] };
        }
        if (element.classList.contains('heading-capitulo') || element.classList.contains('C')) {
          return { type: 'capitulo', match: [text] };
        }
        if (element.classList.contains('heading-secao') || element.classList.contains('S')) {
          return { type: 'secao', match: [text] };
        }
        if (element.classList.contains('subsecao') || element.classList.contains('SS')) {
          return { type: 'subsecao', match: [text] };
        }
      }

      return null;
    };

    // Buscar todos os elementos potenciais, incluindo elementos com classes específicas
    const allElements = Array.from(temp.querySelectorAll(`
      p, div, h1, h2, h3, h4, h5, h6, strong,
      [class*="heading"], [id^="art-"],
      .L, .T, .C, .S, .SS,
      [class*="livro"], [class*="titulo"], [class*="capitulo"], [class*="secao"],
      [style*="font-weight"], [style*="bold"],
      .center, .centered, [align="center"]
    `));
    
    // Stack para manter a hierarquia
    const hierarchyStack: SystematicIndexItem[] = [];

    allElements.forEach((element, index) => {
      const text = element.textContent?.trim() || '';
      if (!text) return;

      let item: SystematicIndexItem | null = null;

      // Verificar se é um artigo (prioridade alta)
      if (element.id && element.id.startsWith('art-')) {
        const articleMatch = text.match(/^Art\.?\s*(\d+)/i);
        if (articleMatch) {
          item = {
            id: element.id,
            title: `Art. ${articleMatch[1]}`,
            type: 'artigo',
            level: 5,
            element: element as HTMLElement
          };
        }
      }
      // Verificar outros tipos hierárquicos
      else {
        const detection = detectHierarchyType(text, element);
        if (detection) {
          const levelMap = {
            livro: 0,
            titulo: 1,
            capitulo: 2,
            secao: 3,
            subsecao: 4
          };

          item = {
            id: `${detection.type}-${index}`,
            title: text.length > 100 ? text.substring(0, 100) + '...' : text,
            type: detection.type as any,
            level: levelMap[detection.type as keyof typeof levelMap],
            children: [],
            element: element as HTMLElement
          };
        }
      }

      if (item) {
        totalItems++;

        // Ajustar a hierarquia
        while (hierarchyStack.length > 0 && hierarchyStack[hierarchyStack.length - 1].level >= item.level) {
          hierarchyStack.pop();
        }

        // Adicionar ao pai apropriado ou à raiz
        if (hierarchyStack.length > 0) {
          const parent = hierarchyStack[hierarchyStack.length - 1];
          if (!parent.children) parent.children = [];
          parent.children.push(item);
        } else {
          items.push(item);
        }

        // Adicionar à stack se não for artigo (artigos não têm filhos)
        if (item.type !== 'artigo') {
          hierarchyStack.push(item);
        }
      }
    });

    setStructure({ items, totalItems });
  }, [htmlRemoto]);

  return structure;
}
