import { useState, useEffect } from 'react';

export interface SystematicIndexItem {
  id: string;
  title: string;
  type: 'livro' | 'titulo' | 'capitulo' | 'secao' | 'subsecao' | 'artigo';
  level: number;
  children?: SystematicIndexItem[];
  element?: HTMLElement;
}

export interface SystematicIndexStructure {
  items: SystematicIndexItem[];
  totalItems: number;
}

/**
 * Hook para extrair a estrutura hierárquica completa de uma lei
 * Analisa o HTML e constrói um índice sistemático com todos os níveis
 */
export function useSystematicIndex(htmlRemoto: string | null): SystematicIndexStructure {
  const [structure, setStructure] = useState<SystematicIndexStructure>({
    items: [],
    totalItems: 0
  });

  useEffect(() => {
    if (!htmlRemoto) {
      setStructure({ items: [], totalItems: 0 });
      return;
    }

    // Criar um elemento temporário para parsing
    const temp = document.createElement('div');
    temp.innerHTML = htmlRemoto;

    const items: SystematicIndexItem[] = [];
    let totalItems = 0;

    // Padrões para identificar diferentes tipos de elementos hierárquicos
    const patterns = {
      livro: /^(LIVRO|PARTE)\s+([IVX\d]+)/i,
      titulo: /^T[ÍI]TULO\s+([IVX\d]+)/i,
      capitulo: /^CAP[ÍI]TULO\s+([IVX\d]+)/i,
      secao: /^SE[ÇC][ÃA]O\s+([IVX\d]+)/i,
      subsecao: /^SUBSE[ÇC][ÃA]O\s+([IVX\d]+)/i,
      artigo: /^Art\.?\s*(\d+)/i
    };

    // Padrões alternativos para capturar mais variações
    const alternativePatterns = {
      livro: /^(LIVRO|PARTE)\s*[-–—]\s*/i,
      titulo: /^T[ÍI]TULO\s*[-–—]\s*/i,
      capitulo: /^CAP[ÍI]TULO\s*[-–—]\s*/i,
      secao: /^SE[ÇC][ÃA]O\s*[-–—]\s*/i,
      subsecao: /^SUBSE[ÇC][ÃA]O\s*[-–—]\s*/i
    };

    // Buscar todos os elementos potenciais, incluindo elementos com classes específicas
    const allElements = Array.from(temp.querySelectorAll(`
      p, div, h1, h2, h3, h4, h5, h6, strong,
      [class*="heading"], [id^="art-"],
      .L, .T, .C, .S, .SS,
      [class*="livro"], [class*="titulo"], [class*="capitulo"], [class*="secao"],
      [style*="font-weight"], [style*="bold"],
      .center, .centered, [align="center"]
    `));
    
    // Stack para manter a hierarquia
    const hierarchyStack: SystematicIndexItem[] = [];

    allElements.forEach((element, index) => {
      const text = element.textContent?.trim() || '';
      if (!text) return;

      let item: SystematicIndexItem | null = null;

      // Verificar se é um artigo (prioridade alta)
      if (element.id && element.id.startsWith('art-')) {
        const match = text.match(patterns.artigo);
        if (match) {
          item = {
            id: element.id,
            title: `Art. ${match[1]}`,
            type: 'artigo',
            level: 5,
            element: element as HTMLElement
          };
        }
      }
      // Verificar outros tipos hierárquicos
      else {
        // Primeiro tentar padrões principais
        for (const [type, pattern] of Object.entries(patterns)) {
          if (type === 'artigo') continue; // Já verificado acima

          let match = text.match(pattern);

          // Se não encontrou, tentar padrões alternativos
          if (!match && alternativePatterns[type as keyof typeof alternativePatterns]) {
            match = text.match(alternativePatterns[type as keyof typeof alternativePatterns]);
          }

          if (match) {
            const levelMap = {
              livro: 0,
              titulo: 1,
              capitulo: 2,
              secao: 3,
              subsecao: 4
            };

            item = {
              id: `${type}-${index}`,
              title: text.length > 100 ? text.substring(0, 100) + '...' : text,
              type: type as any,
              level: levelMap[type as keyof typeof levelMap],
              children: [],
              element: element as HTMLElement
            };
            break;
          }
        }
      }

      if (item) {
        totalItems++;

        // Ajustar a hierarquia
        while (hierarchyStack.length > 0 && hierarchyStack[hierarchyStack.length - 1].level >= item.level) {
          hierarchyStack.pop();
        }

        // Adicionar ao pai apropriado ou à raiz
        if (hierarchyStack.length > 0) {
          const parent = hierarchyStack[hierarchyStack.length - 1];
          if (!parent.children) parent.children = [];
          parent.children.push(item);
        } else {
          items.push(item);
        }

        // Adicionar à stack se não for artigo (artigos não têm filhos)
        if (item.type !== 'artigo') {
          hierarchyStack.push(item);
        }
      }
    });

    setStructure({ items, totalItems });
  }, [htmlRemoto]);

  return structure;
}
