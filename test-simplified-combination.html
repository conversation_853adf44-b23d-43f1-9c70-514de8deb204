<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste - Combinação Simplificada</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a9e;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            max-height: 400px;
            overflow-y: auto;
        }
        
        /* Estilos dos títulos */
        .combined-title {
            padding: 0.7rem 1rem !important;
            margin: 1.5rem 0 1rem 0 !important;
            border-radius: 4px !important;
            font-weight: 600 !important;
            font-size: 0.95em !important;
            letter-spacing: 0.3px !important;
            text-transform: uppercase !important;
            color: #f1f5f9 !important;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12) !important;
            text-align: left !important;
            line-height: 1.3 !important;
            border-left: 3px solid !important;
        }
        
        .title-livro {
            border-left-color: #ef4444 !important;
            background: rgba(127, 29, 29, 0.85) !important;
        }
        
        .title-titulo {
            border-left-color: #3b82f6 !important;
            background: rgba(30, 58, 138, 0.85) !important;
        }
        
        .title-capitulo {
            border-left-color: #10b981 !important;
            background: rgba(6, 78, 59, 0.85) !important;
        }
        
        .title-secao {
            border-left-color: #f59e0b !important;
            background: rgba(146, 64, 14, 0.85) !important;
        }
        
        .title-parte {
            border-left-color: #8b5cf6 !important;
            background: rgba(91, 33, 182, 0.85) !important;
        }
        
        .heading {
            background: rgba(251, 191, 36, 0.08) !important;
            border-left: 4px solid #fde68a !important;
            padding: 0.5em 1em !important;
            margin-top: 2em !important;
            margin-bottom: 1.2em !important;
            border-radius: 0.4em !important;
            font-weight: 600 !important;
        }
    </style>
</head>
<body>
    <h1>Teste - Combinação Simplificada</h1>
    
    <div class="container">
        <h2>Estrutura de Teste (Baseada na Imagem Real)</h2>
        <div id="test-container">
            <p class="heading">TÍTULO I</p>
            <p>Das Disposições Preliminares</p>
            <p class="heading">LIVRO I</p>
            <p>DAS NORMAS PROCESSUAIS CIVIS</p>
            <p class="heading">TÍTULO ÚNICO</p>
            <p>DAS NORMAS FUNDAMENTAIS E DA APLICAÇÃO DAS NORMAS PROCESSUAIS</p>
            <p class="heading">CAPÍTULO I</p>
            <p>DAS NORMAS FUNDAMENTAIS DO PROCESSO CIVIL</p>
        </div>
        
        <button onclick="testSimplifiedCombination()">Testar Combinação Simplificada</button>
        <button onclick="resetTest()">Resetar Teste</button>
        <button onclick="analyzeStructure()">Analisar Estrutura</button>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        function showResult(message) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.style.display = 'block';
        }

        function getTitleType(text) {
            const upperText = text.toUpperCase();
            if (upperText.includes('LIVRO')) return 'title-livro';
            if (upperText.includes('TÍTULO') || upperText.includes('TITULO')) return 'title-titulo';
            if (upperText.includes('CAPÍTULO') || upperText.includes('CAPITULO')) return 'title-capitulo';
            if (upperText.includes('SEÇÃO') || upperText.includes('SECAO')) return 'title-secao';
            if (upperText.includes('PARTE')) return 'title-parte';
            return 'title-default';
        }

        function testSimplifiedCombination() {
            const container = document.getElementById('test-container');
            const paragraphs = Array.from(container.querySelectorAll('p'));
            
            let logs = 'TESTE DA COMBINAÇÃO SIMPLIFICADA:\n\n';
            logs += `📊 Total paragraphs: ${paragraphs.length}\n\n`;
            
            const processedTitlePattern = /^(LIVRO|PARTE|T[ÍI]TULO|CAP[ÍI]TULO|SE[ÇC][ÃA]O)\s+/i;
            const toRemove = [];
            
            for (let i = 0; i < paragraphs.length - 1; i++) {
                const current = paragraphs[i];
                const next = paragraphs[i + 1];
                
                if (toRemove.includes(current)) continue;
                
                const currentText = current.textContent?.trim() || '';
                const nextText = next.textContent?.trim() || '';
                
                logs += `🔍 Pair ${i}: "${currentText}" (${current.className}) + "${nextText}" (${next.className})\n`;
                
                // Verificar condições simplificadas
                const hasHeading = current.classList.contains('heading');
                const matchesPattern = processedTitlePattern.test(currentText);
                const nextHasText = nextText && nextText.length > 3;
                const nextStartsWithUpper = /^[A-ZÁÉÍÓÚÂÊÎÔÛÀÈÌÒÙÃÕÇ]/.test(nextText);
                const nextNotArticle = !nextText.includes('Art.');
                const nextNotParagraph = !nextText.includes('§');
                
                logs += `  hasHeading: ${hasHeading}\n`;
                logs += `  matchesPattern: ${matchesPattern}\n`;
                logs += `  nextHasText: ${nextHasText}\n`;
                logs += `  nextStartsWithUpper: ${nextStartsWithUpper}\n`;
                logs += `  nextNotArticle: ${nextNotArticle}\n`;
                logs += `  nextNotParagraph: ${nextNotParagraph}\n`;
                
                const shouldCombine = hasHeading && matchesPattern && nextHasText && 
                                    nextStartsWithUpper && nextNotArticle && nextNotParagraph;
                
                logs += `  shouldCombine: ${shouldCombine}\n`;
                
                if (shouldCombine) {
                    const combinedTitle = `${currentText.toUpperCase()} - ${nextText.toUpperCase()}`;
                    const titleType = getTitleType(currentText);
                    
                    logs += `  ✅ COMBINING: "${combinedTitle}" -> ${titleType}\n`;
                    
                    current.innerHTML = combinedTitle;
                    current.className = `combined-title ${titleType}`;
                    toRemove.push(next);
                } else {
                    logs += `  ❌ Not combining\n`;
                }
                
                logs += '\n';
            }
            
            // Remover elementos
            toRemove.forEach(element => element.remove());
            logs += `🗑️ Removed ${toRemove.length} elements\n\n`;
            
            // Verificar resultado
            const finalTitles = container.querySelectorAll('.combined-title');
            logs += `✅ Final result: ${finalTitles.length} combined titles\n`;
            finalTitles.forEach((title, index) => {
                logs += `📝 Title ${index + 1}: "${title.textContent}" -> ${title.className}\n`;
            });
            
            showResult(logs);
        }

        function resetTest() {
            const container = document.getElementById('test-container');
            container.innerHTML = `
                <p class="heading">TÍTULO I</p>
                <p>Das Disposições Preliminares</p>
                <p class="heading">LIVRO I</p>
                <p>DAS NORMAS PROCESSUAIS CIVIS</p>
                <p class="heading">TÍTULO ÚNICO</p>
                <p>DAS NORMAS FUNDAMENTAIS E DA APLICAÇÃO DAS NORMAS PROCESSUAIS</p>
                <p class="heading">CAPÍTULO I</p>
                <p>DAS NORMAS FUNDAMENTAIS DO PROCESSO CIVIL</p>
            `;
            showResult('Teste resetado para o estado inicial.');
        }

        function analyzeStructure() {
            const container = document.getElementById('test-container');
            const paragraphs = container.querySelectorAll('p');
            
            let analysis = 'ANÁLISE DA ESTRUTURA ATUAL:\n\n';
            
            paragraphs.forEach((p, index) => {
                const text = p.textContent?.trim() || '';
                analysis += `Parágrafo ${index + 1}:\n`;
                analysis += `  Texto: "${text}"\n`;
                analysis += `  Classes: "${p.className}"\n`;
                analysis += `  HTML: ${p.outerHTML}\n\n`;
            });
            
            showResult(analysis);
        }
    </script>
</body>
</html>
