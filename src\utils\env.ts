export const BACKEND_URL =
  import.meta.env.VITE_BACKEND_URL || 'http://localhost:3005';

export const STRIPE_BACKEND_URL =
  import.meta.env.VITE_STRIPE_BACKEND_URL || 'http://localhost:4242';

export const BASE_PATH = import.meta.env.BASE_URL || '/';

export function getOpenAIKey(): string | undefined {
  return import.meta.env.VITE_OPENAI_API_KEY as string | undefined;
}

export function getGeminiKey(): string | undefined {
  return import.meta.env.VITE_GEMINI_API_KEY as string | undefined;
}


export const LAW_CACHE_MAX_DAYS = parseInt(
  import.meta.env.VITE_LAW_CACHE_MAX_DAYS || '30',
  10
);

export function getStripePublishableKey(): string {
  const key = import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY;
  if (!key) {
    throw new Error('VITE_STRIPE_PUBLISHABLE_KEY environment variable is required');
  }
  return key;
}

