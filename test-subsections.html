<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste - Subseções</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a9e;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            max-height: 400px;
            overflow-y: auto;
        }
        
        /* Estilos dos títulos */
        .combined-title {
            padding: 0.7rem 1rem !important;
            margin: 1.5rem 0 1rem 0 !important;
            border-radius: 4px !important;
            font-weight: 600 !important;
            font-size: 0.95em !important;
            letter-spacing: 0.3px !important;
            text-transform: uppercase !important;
            color: #f1f5f9 !important;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12) !important;
            text-align: left !important;
            line-height: 1.3 !important;
            border-left: 3px solid !important;
        }
        
        .title-livro {
            border-left-color: #ef4444 !important;
            background: rgba(127, 29, 29, 0.85) !important;
        }
        
        .title-titulo {
            border-left-color: #3b82f6 !important;
            background: rgba(30, 58, 138, 0.85) !important;
        }
        
        .title-capitulo {
            border-left-color: #10b981 !important;
            background: rgba(6, 78, 59, 0.85) !important;
        }
        
        .title-secao {
            border-left-color: #f59e0b !important;
            background: rgba(146, 64, 14, 0.85) !important;
        }
        
        .title-parte {
            border-left-color: #8b5cf6 !important;
            background: rgba(91, 33, 182, 0.85) !important;
        }
        
        .heading {
            background: rgba(251, 191, 36, 0.08) !important;
            border-left: 4px solid #fde68a !important;
            padding: 0.5em 1em !important;
            margin-top: 2em !important;
            margin-bottom: 1.2em !important;
            border-radius: 0.4em !important;
            font-weight: 600 !important;
        }
    </style>
</head>
<body>
    <h1>Teste - Detecção de Subseções</h1>
    
    <div class="container">
        <h2>Estrutura com Subseções</h2>
        <div id="test-container">
            <!-- Estrutura com subseções que devem ser detectadas -->
            <p class="heading">SEÇÃO II</p>
            <p>DA PROVA TESTEMUNHAL</p>
            <p class="heading">SUBSEÇÃO I</p>
            <p>DA ADMISSIBILIDADE E DO VALOR DA PROVA TESTEMUNHAL</p>
            <p class="heading">Alegação</p>
            <p>Das regras gerais</p>
            <p class="heading">Da Produção</p>
            <p>Dos procedimentos específicos</p>
            <p class="heading">Do Valor</p>
            <p>Da avaliação probatória</p>
        </div>
        
        <button onclick="testSubsections()">Testar Detecção de Subseções</button>
        <button onclick="resetTest()">Resetar Teste</button>
        <button onclick="showHTML()">Mostrar HTML</button>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        function showResult(message) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.style.display = 'block';
        }

        function getTitleType(text) {
            const upperText = text.toUpperCase();
            if (upperText.includes('LIVRO')) return 'title-livro';
            if (upperText.includes('TÍTULO') || upperText.includes('TITULO')) return 'title-titulo';
            if (upperText.includes('CAPÍTULO') || upperText.includes('CAPITULO')) return 'title-capitulo';
            if (upperText.includes('SEÇÃO') || upperText.includes('SECAO')) return 'title-secao';
            if (upperText.includes('SUBSEÇÃO') || upperText.includes('SUBSECAO')) return 'title-secao';
            if (upperText.includes('PARTE')) return 'title-parte';
            // Subseções menores (Alegação, Da/Do/Das/Dos + palavra)
            if (/^(ALEGAÇÃO|DA\s+\w+|DO\s+\w+|DAS\s+\w+|DOS\s+\w+)/i.test(text)) return 'title-secao';
            return 'title-default';
        }

        function testSubsections() {
            const container = document.getElementById('test-container');
            const paragraphs = Array.from(container.querySelectorAll('p'));
            
            let logs = 'TESTE DE DETECÇÃO DE SUBSEÇÕES:\n\n';
            logs += `📊 Total paragraphs: ${paragraphs.length}\n\n`;
            
            // Mostrar estrutura inicial
            logs += 'ESTRUTURA INICIAL:\n';
            paragraphs.forEach((p, index) => {
                logs += `${index}: "${p.textContent}" (${p.className})\n`;
            });
            logs += '\n';
            
            const toRemove = [];
            
            // Processar combinações
            for (let i = 0; i < paragraphs.length - 1; i++) {
                const current = paragraphs[i];
                const next = paragraphs[i + 1];
                
                if (toRemove.includes(current) || toRemove.includes(next)) continue;
                
                const currentText = current.textContent?.trim() || '';
                const nextText = next.textContent?.trim() || '';
                
                logs += `🔍 Pair ${i}: "${currentText}" + "${nextText}"\n`;
                
                // Verificar condições
                const isCurrentHeading = current.classList.contains('heading');
                const isCurrentTitle = /^(LIVRO|PARTE|T[ÍI]TULO|CAP[ÍI]TULO|SE[ÇC][ÃA]O|SUBSE[ÇC][ÃA]O)\s+/i.test(currentText);
                const isSubsection = /^(Alegação|Da\s+\w+|Do\s+\w+|Das\s+\w+|Dos\s+\w+)$/i.test(currentText);
                const hasNextText = nextText && nextText.length > 2;
                const nextNotHeading = !next.classList.contains('heading');
                const nextNotArticle = !/^Art\.|^§|^\d+[º°]/.test(nextText);
                
                logs += `  isCurrentHeading: ${isCurrentHeading}\n`;
                logs += `  isCurrentTitle: ${isCurrentTitle}\n`;
                logs += `  isSubsection: ${isSubsection}\n`;
                logs += `  hasNextText: ${hasNextText}\n`;
                logs += `  nextNotHeading: ${nextNotHeading}\n`;
                logs += `  nextNotArticle: ${nextNotArticle}\n`;
                
                const shouldCombine = (isCurrentHeading && (isCurrentTitle || isSubsection)) && hasNextText && nextNotHeading && nextNotArticle;
                logs += `  shouldCombine: ${shouldCombine}\n`;
                
                if (shouldCombine) {
                    const combinedTitle = `${currentText.toUpperCase()} - ${nextText.toUpperCase()}`;
                    const titleType = getTitleType(currentText);
                    
                    logs += `  ✅ COMBINING: "${combinedTitle}" -> ${titleType}\n`;
                    
                    current.innerHTML = combinedTitle;
                    current.className = `combined-title ${titleType}`;
                    toRemove.push(next);
                } else {
                    logs += `  ❌ Not combining\n`;
                }
                
                logs += '\n';
            }
            
            // Remover elementos
            toRemove.forEach(element => element.remove());
            logs += `🗑️ Removed ${toRemove.length} elements\n\n`;
            
            // Verificar resultado
            const finalTitles = container.querySelectorAll('.combined-title');
            logs += `✅ Final result: ${finalTitles.length} combined titles\n`;
            finalTitles.forEach((title, index) => {
                logs += `📝 Title ${index + 1}: "${title.textContent}" -> ${title.className}\n`;
            });
            
            showResult(logs);
        }

        function resetTest() {
            const container = document.getElementById('test-container');
            container.innerHTML = `
                <p class="heading">SEÇÃO II</p>
                <p>DA PROVA TESTEMUNHAL</p>
                <p class="heading">SUBSEÇÃO I</p>
                <p>DA ADMISSIBILIDADE E DO VALOR DA PROVA TESTEMUNHAL</p>
                <p class="heading">Alegação</p>
                <p>Das regras gerais</p>
                <p class="heading">Da Produção</p>
                <p>Dos procedimentos específicos</p>
                <p class="heading">Do Valor</p>
                <p>Da avaliação probatória</p>
            `;
            showResult('Teste resetado para o estado inicial.');
        }

        function showHTML() {
            const container = document.getElementById('test-container');
            showResult('HTML ATUAL:\n\n' + container.innerHTML);
        }
    </script>
</body>
</html>
