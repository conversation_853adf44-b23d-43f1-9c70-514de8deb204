/**
 * Simple Dependency Injection Container
 * Follows Dependency Inversion Principle
 */

type ServiceFactory<T = any> = () => T;
type ServiceInstance<T = any> = T;

export class ServiceContainer {
  private static instance: ServiceContainer;
  private services = new Map<string, ServiceFactory | ServiceInstance>();
  private singletons = new Map<string, any>();

  private constructor() {}

  static getInstance(): ServiceContainer {
    if (!ServiceContainer.instance) {
      ServiceContainer.instance = new ServiceContainer();
    }
    return ServiceContainer.instance;
  }

  /**
   * Register a service factory
   */
  register<T>(key: string, factory: ServiceFactory<T>): void {
    this.services.set(key, factory);
  }

  /**
   * Register a singleton service
   */
  registerSingleton<T>(key: string, factory: ServiceFactory<T>): void {
    this.services.set(key, factory);
    // Mark as singleton by storing in singletons map
    this.singletons.set(key, null);
  }

  /**
   * Register an instance directly
   */
  registerInstance<T>(key: string, instance: T): void {
    this.services.set(key, instance);
    this.singletons.set(key, instance);
  }

  /**
   * Resolve a service
   */
  resolve<T>(key: string): T {
    if (!this.services.has(key)) {
      throw new Error(`Service '${key}' not registered`);
    }

    // Check if it's a singleton and already instantiated
    if (this.singletons.has(key)) {
      const existing = this.singletons.get(key);
      if (existing !== null) {
        return existing;
      }
    }

    const serviceOrFactory = this.services.get(key)!;

    // If it's already an instance, return it
    if (typeof serviceOrFactory !== 'function') {
      return serviceOrFactory as T;
    }

    // Create new instance
    const instance = (serviceOrFactory as ServiceFactory<T>)();

    // Store singleton if needed
    if (this.singletons.has(key)) {
      this.singletons.set(key, instance);
    }

    return instance;
  }

  /**
   * Check if service is registered
   */
  has(key: string): boolean {
    return this.services.has(key);
  }

  /**
   * Clear all services (useful for testing)
   */
  clear(): void {
    this.services.clear();
    this.singletons.clear();
  }
}

/**
 * Service keys for type safety
 */
export const ServiceKeys = {
  HIGHLIGHT_STORAGE: 'highlightStorage',
  HIGHLIGHT_RENDERER: 'highlightRenderer',
  LAW_FETCHER: 'lawFetcher',
  CACHE_MANAGER: 'cacheManager',
  LOGGER: 'logger',
} as const;

/**
 * Helper function to get service container
 */
export const container = ServiceContainer.getInstance();

/**
 * Decorator for dependency injection (if using TypeScript decorators)
 */
export function inject(serviceKey: string) {
  return function (target: any, propertyKey: string) {
    Object.defineProperty(target, propertyKey, {
      get() {
        return container.resolve(serviceKey);
      },
      enumerable: true,
      configurable: true,
    });
  };
}

/**
 * Hook for dependency injection in React components
 */
export function useService<T>(serviceKey: string): T {
  return container.resolve<T>(serviceKey);
}

/**
 * Abstract interfaces for services
 */
export interface ILogger {
  debug(message: string, ...args: any[]): void;
  info(message: string, ...args: any[]): void;
  warn(message: string, ...args: any[]): void;
  error(message: string, ...args: any[]): void;
}

export interface ICacheManager {
  get<T>(key: string): T | null;
  set<T>(key: string, value: T, ttl?: number): void;
  remove(key: string): void;
  clear(): void;
}

export interface ILawFetcher {
  fetchLaw(leiId: string): Promise<string>;
  fetchLawMeta(leiId: string): Promise<any>;
}

/**
 * Default implementations
 */
export class ConsoleLogger implements ILogger {
  debug(message: string, ...args: any[]): void {
    if (import.meta.env.DEV) {
      console.debug(message, ...args);
    }
  }

  info(message: string, ...args: any[]): void {
    console.info(message, ...args);
  }

  warn(message: string, ...args: any[]): void {
    console.warn(message, ...args);
  }

  error(message: string, ...args: any[]): void {
    console.error(message, ...args);
  }
}

export class MemoryCacheManager implements ICacheManager {
  private cache = new Map<string, { value: any; expires?: number }>();

  get<T>(key: string): T | null {
    const item = this.cache.get(key);
    if (!item) return null;

    if (item.expires && Date.now() > item.expires) {
      this.cache.delete(key);
      return null;
    }

    return item.value;
  }

  set<T>(key: string, value: T, ttl?: number): void {
    const expires = ttl ? Date.now() + ttl : undefined;
    this.cache.set(key, { value, expires });
  }

  remove(key: string): void {
    this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }
}

/**
 * Setup default services
 */
export function setupDefaultServices(): void {
  container.registerSingleton(ServiceKeys.LOGGER, () => new ConsoleLogger());
  container.registerSingleton(ServiceKeys.CACHE_MANAGER, () => new MemoryCacheManager());
}
