import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { signOut, updateProfile, onAuthStateChanged, User } from "firebase/auth";
import { auth } from "../firebase";
import { getOpenAIKey, getGeminiKey } from "../utils/env";
import { ThemeToggle } from "../components/ui/ThemeToggle";
import toast from "react-hot-toast";

// Helper para exibir UID mais curto
function shortUid(uid?: string) {
  return uid ? uid.substring(0, 6) + "…" : "—";
}

interface ProfileScreenProps {
  /**
   * Quando "minimal" estiver ativo, o componente será usado dentro de um modal.
   * Nesse modo, ocultamos elementos de UI que não fazem sentido (ex: ThemeToggle)
   * e evitamos navegação automática entre rotas.
   */
  minimal?: boolean;
  /** Callback para fechar o modal, usado em conjunto com minimal */
  onClose?: () => void;
}

export function ProfileScreen({ minimal = false, onClose }: ProfileScreenProps) {
  const navigate = useNavigate();
  const [user, setUser] = useState<User | null | undefined>(undefined);
  const [credits, setCredits] = useState<number>(() => {
    const raw = localStorage.getItem("ia_credits_left");
    return raw ? parseInt(raw, 10) : 0;
  });
  const [editMode, setEditMode] = useState(false);
  const [displayName, setDisplayName] = useState<string>(() => auth?.currentUser?.displayName || "");
  const [photoURL, setPhotoURL] = useState<string>(() => auth?.currentUser?.photoURL || "");
  const [saving, setSaving] = useState(false);

  // Detecta modo convidado (localStorage)
  const guestMode = typeof localStorage !== 'undefined' && localStorage.getItem('guest_mode') === '1';

  useEffect(() => {
    if (!auth) {
      setUser(null);
      return;
    }
    return onAuthStateChanged(auth, (u) => setUser(u));
  }, []);

  useEffect(() => {
    if (minimal) return; // No modo modal não redirecionamos rotas
    if (user === null && !guestMode) {
      navigate('/login', { replace: true });
    }
  }, [user, guestMode, minimal]);

  function handleLogout() {
    if (!auth) return;
    signOut(auth).then(() => {
      toast("Desconectado");
      if (minimal && onClose) {
        onClose();
      } else {
        navigate("/login", { replace: true });
      }
    });
  }

  async function handleSaveProfile() {
    if (!auth || !user) return;
    try {
      setSaving(true);
      await updateProfile(user, { displayName: displayName.trim() || undefined, photoURL: photoURL.trim() || undefined });
      toast.success("Perfil atualizado!");
      setEditMode(false);
    } catch (err: any) {
      toast.error(err.message || "Falha ao atualizar perfil");
    } finally {
      setSaving(false);
    }
  }

  // ==== API KEY helpers ====
  const openaiKey = getOpenAIKey() || '';
  const geminiKey = getGeminiKey() || '';

  const handleSetKey = (provider: 'OPENAI' | 'GEMINI') => {
    const prev = provider === 'OPENAI' ? openaiKey : geminiKey;
    const val = window.prompt(`Informe a chave de API para ${provider}:`, prev);
    if (val == null) return;
    if (provider === 'OPENAI') {
      if (val) localStorage.setItem('VITE_OPENAI_API_KEY', val.trim()); else localStorage.removeItem('VITE_OPENAI_API_KEY');
    } else {
      if (val) localStorage.setItem('VITE_GEMINI_API_KEY', val.trim()); else localStorage.removeItem('VITE_GEMINI_API_KEY');
    }
    toast.success('Chave atualizada!');
    window.location.reload();
  };

  const plan = guestMode && !user ? 'Guest' : 'Free';

  const containerClass = minimal ? 'space-y-6' : 'min-h-screen p-6 max-w-3xl mx-auto';

  return (
    <div className={containerClass}>
      <header className="flex items-center justify-between mb-6 gap-3 flex-wrap">
        <h2 className={`font-semibold ${minimal ? 'text-xl' : 'text-2xl'}`}>Seu Perfil</h2>
        {!minimal && <ThemeToggle />}
        {minimal && onClose && (
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 text-xl"
            aria-label="Fechar"
          >
            ×
          </button>
        )}
      </header>

      {user === undefined && <p>Carregando…</p>}

      {(user || guestMode) && (
        <div className={minimal ? "space-y-4" : "space-y-8"}>
          {/* Section Conta */}
          <section className={`profile-section-bg rounded-xl ${minimal ? 'p-4' : 'p-6'} shadow-sm`}>
            <div className={`flex ${minimal ? 'items-center gap-4' : 'items-start gap-6'}`}>
              {/* Avatar Container */}
              <div className="flex-shrink-0">
                {user?.photoURL ? (
                  <img
                    src={user.photoURL}
                    alt="Avatar"
                    className={`rounded-full object-cover border-2 border-primary/20 ${minimal ? 'w-16 h-16' : 'w-20 h-20'}`}
                  />
                ) : (
                  <div className={`rounded-full bg-primary/40 flex items-center justify-center uppercase font-semibold border-2 border-primary/20 ${minimal ? 'w-16 h-16 text-xl' : 'w-20 h-20 text-2xl'}`}>
                    {user?.displayName?.[0] || 'G'}
                  </div>
                )}
              </div>

              {/* Profile Info Container */}
              <div className="flex-1 min-w-0">
                {user && editMode ? (
                  <div className="space-y-3">
                    <input
                      type="text"
                      value={displayName}
                      onChange={(e)=>setDisplayName(e.target.value)}
                      className="px-3 py-2 rounded-md profile-inner-bg border border-gray-300 dark:border-white/20 theme-sepia:border-amber-300 theme-ocean:border-slate-400 theme-forest:border-green-400 theme-sunset:border-orange-300 w-full"
                      placeholder="Nome"
                    />
                    <input
                      type="text"
                      value={photoURL}
                      onChange={(e)=>setPhotoURL(e.target.value)}
                      className="px-3 py-2 rounded-md profile-inner-bg border border-gray-300 dark:border-white/20 theme-sepia:border-amber-300 theme-ocean:border-slate-400 theme-forest:border-green-400 theme-sunset:border-orange-300 w-full"
                      placeholder="URL da foto (opcional)"
                    />
                    <div className="flex gap-2 pt-1">
                      <button
                        onClick={handleSaveProfile}
                        disabled={saving}
                        className="px-4 py-2 rounded-md bg-primary/80 hover:bg-primary transition-colors text-white font-medium"
                      >
                        {saving ? 'Salvando...' : 'Salvar'}
                      </button>
                      <button
                        onClick={()=>setEditMode(false)}
                        className="px-4 py-2 rounded-md profile-inner-bg hover:bg-gray-300 dark:hover:bg-white/20 transition-colors"
                      >
                        Cancelar
                      </button>
                    </div>
                  </div>
                ) : (
                  <div className={`${minimal ? 'space-y-1' : 'space-y-2'}`}>
                    {/* Nome do usuário */}
                    <div>
                      <h3 className={`font-semibold text-gray-900 dark:text-white ${minimal ? 'text-lg' : 'text-xl'}`}>
                        {user?.displayName || 'Convidado'}
                      </h3>
                    </div>

                    {/* Email */}
                    {user?.email && (
                      <div>
                        <p className="text-sm text-gray-600 dark:text-gray-300 break-all">
                          {user.email}
                        </p>
                      </div>
                    )}

                    {/* UID */}
                    <div>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        UID: {shortUid(user?.uid)}
                      </p>
                    </div>

                    {/* Botões de ação */}
                    <div className="pt-2">
                      {user && (
                        <button
                          onClick={()=>setEditMode(true)}
                          className="text-sm text-primary hover:text-primary-dark underline transition-colors"
                        >
                          Editar perfil
                        </button>
                      )}
                      {!user && guestMode && (
                        <button
                          onClick={()=>navigate('/login')}
                          className="text-sm text-primary hover:text-primary-dark underline transition-colors"
                        >
                          Fazer login / Criar conta
                        </button>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </section>

          {/* Section Plano */}
          <section className={`profile-section-bg rounded-xl ${minimal ? 'p-4' : 'p-6'} flex items-center justify-between shadow-sm`}>
            <div>
              <h3 className={`font-semibold mb-1 ${minimal ? 'text-sm' : ''}`}>Plano Atual</h3>
              <p className={`opacity-80 capitalize ${minimal ? 'text-sm' : ''}`}>{plan}</p>
            </div>
            <button onClick={()=>toast('Em breve 📈')} className={`rounded-md bg-amber-500 hover:bg-amber-400 transition-colors text-neutral-900 ${minimal ? 'px-3 py-1 text-sm' : 'px-4 py-2'}`}>Fazer upgrade</button>
          </section>

          {/* Section Créditos IA */}
          <section className={`profile-section-bg rounded-xl ${minimal ? 'p-4' : 'p-6'} flex items-center justify-between shadow-sm`}>
            <div>
              <h3 className={`font-semibold mb-1 ${minimal ? 'text-sm' : ''}`}>Créditos de IA</h3>
              <p className={`opacity-80 ${minimal ? 'text-sm' : ''}`}>{credits} restantes</p>
            </div>
            <button onClick={()=>toast('Comprar créditos – em breve')} className={`rounded-md bg-emerald-600 hover:bg-emerald-500 transition-colors ${minimal ? 'px-3 py-1 text-sm' : 'px-4 py-2'}`}>Comprar mais</button>
          </section>

          {/* Section API Keys */}
          <section className={`profile-section-bg rounded-xl ${minimal ? 'p-4 space-y-3' : 'p-6 space-y-4'} shadow-sm`}>
            <h3 className="font-semibold">Chaves de API</h3>
            <div className="grid sm:grid-cols-2 gap-4">
              {/* OpenAI */}
              <div className="profile-inner-bg p-4 rounded-lg flex items-center justify-between">
                <div>
                  <p className="font-medium">OpenAI</p>
                  <p className="text-xs opacity-70 break-all">{openaiKey ? shortUid(openaiKey) : '—'}</p>
                </div>
                <button onClick={()=>handleSetKey('OPENAI')} className="px-3 py-1 rounded-md bg-primary/80 hover:bg-primary text-sm">{openaiKey ? 'Alterar' : 'Adicionar'}</button>
              </div>
              {/* Gemini */}
              <div className="profile-inner-bg p-4 rounded-lg flex items-center justify-between">
                <div>
                  <p className="font-medium">Gemini</p>
                  <p className="text-xs opacity-70 break-all">{geminiKey ? shortUid(geminiKey) : '—'}</p>
                </div>
                <button onClick={()=>handleSetKey('GEMINI')} className="px-3 py-1 rounded-md bg-primary/80 hover:bg-primary text-sm">{geminiKey ? 'Alterar' : 'Adicionar'}</button>
              </div>
            </div>
          </section>

          {/* Botão logout */}
          <div className="text-center">
            <button onClick={handleLogout} className="mt-4 px-6 py-2 rounded-md bg-red-500 hover:bg-red-400 transition-colors">Sair</button>
          </div>
        </div>
      )}
    </div>
  );
} 