import React, { useState, useRef, useEffect } from 'react';



interface HierarchicalNavigationProps {
  context: string;
  debugInfo?: {
    totalHeadings: number;
    visibleHeadings: number;
    currentScroll: number;
    detectedElements: string[];
  };
  onHide: () => void;
}

export function HierarchicalNavigation({ context, debugInfo, onHide }: HierarchicalNavigationProps) {
  const [isExpanded, setIsExpanded] = useState(true);
  const [showDebug, setShowDebug] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Detect mobile device
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);



  // Parse context into hierarchy levels
  const hierarchyLevels = context ? context.split(' • ').filter(Boolean) : [];
  const uniqLevels = hierarchyLevels.filter((v, i, a) => a.indexOf(v) === i);

  // Function to find DOM element for a hierarchical level
  const findElementForLevel = (level: string): HTMLElement | null => {
    const cleanLevel = level.replace(/^(L\.|T\.|C\.|S\.|SS\.)\s*/, '').trim();
    const levelLower = level.toLowerCase();

    // First try class-based selectors
    let classSelectors: string[] = [];

    if (levelLower.startsWith('l.')) {
      classSelectors = ['.L', '[class*="livro"]'];
    } else if (levelLower.startsWith('t.')) {
      classSelectors = ['.T', '[class*="titulo"]'];
    } else if (levelLower.startsWith('c.')) {
      classSelectors = ['.C', '[class*="capitulo"]'];
    } else if (levelLower.startsWith('s.')) {
      classSelectors = ['.S', '[class*="secao"]'];
    } else if (levelLower.startsWith('ss.')) {
      classSelectors = ['.SS', '[class*="subsecao"]'];
    }

    // Try class-based elements first
    for (const selector of classSelectors) {
      const elements = document.querySelectorAll(selector);
      for (const element of elements) {
        const text = element.textContent?.trim() || '';
        if (text.includes(cleanLevel) && text.length < 300) {
          return element as HTMLElement;
        }
      }
    }

    // Then try text-based matching on all potential heading elements
    const allElements = document.querySelectorAll('p, div, h1, h2, h3, h4, h5, h6, strong, .heading, [class*="center"]');
    for (const element of allElements) {
      const text = element.textContent?.trim() || '';

      // Check if this element contains the level text
      if (text.includes(cleanLevel) && text.length < 300) {
        // Additional validation based on level type
        const textUpper = text.toUpperCase();

        if (levelLower.startsWith('l.') && (textUpper.includes('LIVRO') || textUpper.includes('PARTE'))) {
          return element as HTMLElement;
        } else if (levelLower.startsWith('t.') && textUpper.includes('TÍTULO')) {
          return element as HTMLElement;
        } else if (levelLower.startsWith('c.') && textUpper.includes('CAPÍTULO')) {
          return element as HTMLElement;
        } else if (levelLower.startsWith('s.') && textUpper.includes('SEÇÃO')) {
          return element as HTMLElement;
        } else if (levelLower.startsWith('ss.') && textUpper.includes('SUBSEÇÃO')) {
          return element as HTMLElement;
        }
      }
    }

    return null;
  };

  // Function to scroll to a hierarchical level
  const scrollToLevel = (level: string) => {
    const element = findElementForLevel(level);
    if (element) {
      const headerOffset = 80; // Account for fixed headers
      const elementPosition = element.getBoundingClientRect().top + window.pageYOffset;
      const offsetPosition = elementPosition - headerOffset;

      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
      });

      // Add visual feedback - briefly highlight the target section
      element.style.transition = 'background-color 0.3s ease';
      const originalBg = element.style.backgroundColor;
      element.style.backgroundColor = 'rgba(59, 130, 246, 0.3)'; // Blue highlight

      setTimeout(() => {
        element.style.backgroundColor = originalBg;
        setTimeout(() => {
          element.style.transition = '';
        }, 300);
      }, 1000);
    }
  };

  // Enhanced color coding for different levels - now with compact rectangular design
  const getLevelColor = (level: string): string => {
    const levelLower = level.toLowerCase();
    if (levelLower.startsWith('l.')) return 'bg-red-600 hover:bg-red-500 border-red-700';
    if (levelLower.startsWith('t.')) return 'bg-blue-600 hover:bg-blue-500 border-blue-700';
    if (levelLower.startsWith('c.')) return 'bg-green-600 hover:bg-green-500 border-green-700';
    if (levelLower.startsWith('s.')) return 'bg-amber-600 hover:bg-amber-500 border-amber-700';
    if (levelLower.startsWith('ss.')) return 'bg-purple-600 hover:bg-purple-500 border-purple-700';
    return 'bg-gray-600 hover:bg-gray-500 border-gray-700';
  };

  // Formata o texto completo do nível (ex: "L. LIVRO III DAS PESSOAS" -> "LIII. DAS PESSOAS")
  const formatLevelLabel = (level: string): string => {
    const prefixMatch = level.match(/^(L|T|C|S|SS)\./i);
    const prefix = prefixMatch ? prefixMatch[1].toUpperCase() : '';

    // Remove o prefixo inicial (L., T., etc.)
    const withoutPrefix = level.replace(/^(L|T|C|S|SS)\.[\s]*/i, '').trim();

    // Remove as palavras-chave (LIVRO, TÍTULO, CAPÍTULO, SEÇÃO, SUBSEÇÃO)
    const withoutKeyword = withoutPrefix
      .replace(/^(LIVRO|PARTE|T[ÍI]TULO|CAP[ÍI]TULO|SE[ÇC][ÃA]O|SUBSE[ÇC][ÃA]O)[\s]*/i, '')
      .trim();

    const tokens = withoutKeyword.split(/\s+/);

    // encontra primeiro número romano
    let roman = '';
    let romanIdx = -1;
    for (let i = 0; i < tokens.length; i++) {
      if (/^[IVXLCDM]+$/i.test(tokens[i])) {
        roman = tokens[i].toUpperCase();
        romanIdx = i;
        break;
      }
    }

    const restTokens = romanIdx !== -1 ? tokens.slice(romanIdx + 1) : tokens;
    const rest = restTokens.join(' ').toUpperCase();

    if (roman) {
      return `${prefix}${roman}. ${rest}`.trim();
    }
    return `${prefix}. ${rest}`.trim();
  };

  if (!context && !debugInfo) return null;

  // Close navigation when user clicks the ✕ button
  const handleClose = () => onHide();

  return (
    <div
      style={{ left: '2px', top: '2px' }}
      className="fixed z-50 select-none transition-all duration-300"
    >
      {!isExpanded ? (
        <button
          onClick={() => setIsExpanded(true)}
          className="bg-gray-800 bg-opacity-90 backdrop-blur-sm rounded-md shadow-lg border border-gray-600 hover:bg-gray-700 transition-colors p-1.5"
          title="Mostrar navegação hierárquica"
        >
          <span className="text-white text-lg leading-none">⋯</span>
        </button>
      ) : (
        <div className="flex items-center space-x-1 p-1">
          <div className="flex flex-wrap gap-0.5">
            {uniqLevels.map((level, idx) => (
              <button
                key={idx}
                onClick={() => scrollToLevel(level)}
                className={`
                  rounded border transition-all duration-200 cursor-pointer
                  hover:shadow-md hover:scale-105 active:scale-95
                  ${getLevelColor(level)}
                  px-1 py-0.25 h-4 text-[9px] leading-none
                  focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50
                `}
                title={`Navegar para: ${formatLevelLabel(level)}`}
              >
                {formatLevelLabel(level)}
              </button>
            ))}
          </div>
          <button
            onClick={handleClose}
            className="ml-1 p-1 rounded hover:bg-white/10 text-xs text-gray-400 hover:text-white"
            title="Fechar navegação"
          >
            ✕
          </button>
        </div>
      )}
    </div>
  );
}

export default HierarchicalNavigation;
