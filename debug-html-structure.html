<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug - Estrutura HTML</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a9e;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            max-height: 400px;
            overflow-y: auto;
        }
        .highlight {
            background: yellow;
            padding: 2px 4px;
            border-radius: 2px;
        }
    </style>
</head>
<body>
    <h1>Debug - Estrutura HTML Real</h1>
    
    <div class="container">
        <h2>Análise da Estrutura HTML</h2>
        <p>Este teste vai analisar a estrutura HTML real que está sendo processada.</p>
        
        <button onclick="analyzeRealStructure()">Analisar Estrutura Real</button>
        <button onclick="simulateProcessing()">Simular Processamento</button>
        <button onclick="testPatterns()">Testar Padrões Regex</button>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        function showResult(message) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.style.display = 'block';
        }

        function analyzeRealStructure() {
            // Simular a estrutura HTML real baseada na imagem
            const realHtmlSamples = [
                // Estrutura que vemos na imagem
                `<p class="heading">TÍTULO I</p>
<p>Das Disposições Preliminares</p>`,
                
                // Estrutura com classes Cap
                `<p class="heading">LIVRO I</p>
<p class="Cap">DAS NORMAS PROCESSUAIS CIVIS</p>`,
                
                // Estrutura mais complexa
                `<p class="heading heading-titulo">TÍTULO ÚNICO</p>
<p align="center" class="Cap">DAS NORMAS FUNDAMENTAIS E DA APLICAÇÃO DAS NORMAS PROCESSUAIS</p>`,
                
                // Estrutura com links
                `<p class="heading"><a name="titulo1"></a>TÍTULO I</p>
<p class="Cap">Das Disposições Preliminares</p>`
            ];
            
            let analysis = 'ANÁLISE DA ESTRUTURA HTML REAL:\n\n';
            
            realHtmlSamples.forEach((sample, index) => {
                analysis += `=== AMOSTRA ${index + 1} ===\n`;
                analysis += sample + '\n\n';
                
                // Criar um container temporário para análise
                const temp = document.createElement('div');
                temp.innerHTML = sample;
                
                const paragraphs = temp.querySelectorAll('p');
                paragraphs.forEach((p, pIndex) => {
                    const text = p.textContent?.trim() || '';
                    analysis += `Parágrafo ${pIndex + 1}:\n`;
                    analysis += `  Texto: "${text}"\n`;
                    analysis += `  Classes: "${p.className}"\n`;
                    analysis += `  HTML: ${p.outerHTML}\n\n`;
                });
                
                analysis += '---\n\n';
            });
            
            showResult(analysis);
        }

        function simulateProcessing() {
            let logs = 'SIMULAÇÃO DO PROCESSAMENTO:\n\n';
            
            // Simular a estrutura real
            const container = document.createElement('div');
            container.innerHTML = `
                <p class="heading">TÍTULO I</p>
                <p>Das Disposições Preliminares</p>
                <p class="heading">LIVRO I</p>
                <p class="Cap">DAS NORMAS PROCESSUAIS CIVIS</p>
                <p class="heading">CAPÍTULO I</p>
                <p class="Cap">Da Personalidade e da Capacidade</p>
            `;
            
            const paragraphs = Array.from(container.querySelectorAll('p'));
            logs += `📊 Total paragraphs: ${paragraphs.length}\n\n`;
            
            // Padrões
            const patterns = {
                processedTitlePattern: /^(LIVRO|PARTE|T[ÍI]TULO|CAP[ÍI]TULO|SE[ÇC][ÃA]O)\s+/i,
                descPattern: /^[A-ZÁÉÍÓÚÂÊÎÔÛÀÈÌÒÙÃÕÇ]/
            };
            
            const toRemove = [];
            
            for (let i = 0; i < paragraphs.length - 1; i++) {
                const current = paragraphs[i];
                const next = paragraphs[i + 1];
                
                if (toRemove.includes(current)) continue;
                
                const currentText = current.textContent?.trim() || '';
                const nextText = next.textContent?.trim() || '';
                
                logs += `🔍 Pair ${i}: "${currentText}" (${current.className}) + "${nextText}" (${next.className})\n`;
                
                // Verificar condições
                const hasHeading = current.classList.contains('heading');
                const matchesPattern = patterns.processedTitlePattern.test(currentText);
                const nextHasCap = next.classList.contains('Cap');
                const nextHasText = nextText && nextText.length > 3;
                const nextMatchesDesc = patterns.descPattern.test(nextText);
                
                logs += `  hasHeading: ${hasHeading}\n`;
                logs += `  matchesPattern: ${matchesPattern}\n`;
                logs += `  nextHasCap: ${nextHasCap}\n`;
                logs += `  nextHasText: ${nextHasText}\n`;
                logs += `  nextMatchesDesc: ${nextMatchesDesc}\n`;
                
                // Verificar se deve combinar
                const shouldCombine = hasHeading && matchesPattern && nextHasText;
                logs += `  shouldCombine: ${shouldCombine}\n`;
                
                if (shouldCombine) {
                    logs += `  ✅ COMBINING!\n`;
                    toRemove.push(next);
                } else {
                    logs += `  ❌ Not combining\n`;
                }
                
                logs += '\n';
            }
            
            logs += `🗑️ Elements to remove: ${toRemove.length}\n`;
            
            showResult(logs);
        }

        function testPatterns() {
            const testTexts = [
                'TÍTULO I',
                'LIVRO I',
                'CAPÍTULO I',
                'SEÇÃO I',
                'PARTE GERAL',
                'Das Disposições Preliminares',
                'DAS NORMAS PROCESSUAIS CIVIS',
                'Da Personalidade e da Capacidade',
                'Art. 1º',
                'Parágrafo único'
            ];
            
            const patterns = {
                processedTitlePattern: /^(LIVRO|PARTE|T[ÍI]TULO|CAP[ÍI]TULO|SE[ÇC][ÃA]O)\s+/i,
                descPattern: /^[A-ZÁÉÍÓÚÂÊÎÔÛÀÈÌÒÙÃÕÇ]/
            };
            
            let results = 'TESTE DOS PADRÕES REGEX:\n\n';
            
            testTexts.forEach(text => {
                const titleMatch = patterns.processedTitlePattern.test(text);
                const descMatch = patterns.descPattern.test(text);
                
                results += `"${text}":\n`;
                results += `  processedTitlePattern: ${titleMatch}\n`;
                results += `  descPattern: ${descMatch}\n\n`;
            });
            
            showResult(results);
        }
    </script>
</body>
</html>
