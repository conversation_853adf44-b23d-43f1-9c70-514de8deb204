import { useEffect, useState, startTransition, useRef, useMemo } from "react";
import { collection, addDoc, onSnapshot, deleteDoc, doc, writeBatch, updateDoc } from "firebase/firestore";
import { db } from "../firebase";
import { auth } from "../firebase";
import { generateUUID } from "../utils/uuid";
import { enrichHighlight } from "../utils/highlightPersistence";
import { getNodeFromPath } from "./useTextSelection";
import { migrateHighlights, detectLawUpdate, cleanupOrphanedHighlights, validateHighlight, generateHighlightHealthReport } from "../utils/highlightMigration";

export interface Highlight {
  id: string;
  color: string;
  text: string;
  article?: string;
  startContainerPath: number[];
  startOffset: number;
  endContainerPath: number[];
  endOffset: number;
  // Campos adicionais para melhor persistência
  textHash?: string; // Hash do texto para detectar mudanças
  contextBefore?: string; // Texto antes do highlight (para contexto)
  contextAfter?: string; // Texto depois do highlight (para contexto)
  articleContext?: string; // Contexto do artigo completo
  relativePosition?: number; // Posição relativa no artigo (0-1)
  lawVersion?: string; // Versão/hash da lei quando highlight foi criado
  createdAt?: number; // Timestamp de criação
  lastValidated?: number; // Última vez que foi validado
}

// Debug logging utility
const debugLog = (message: string, ...args: any[]) => {
  if (import.meta.env.DEV) {
    console.debug(`[useHighlights] ${message}`, ...args);
  }
};

export function useHighlights(leiId: string) {
  const [user, setUser] = useState<any>(undefined); // undefined = loading, null = no user, object = user
  const [highlights, setHighlights] = useState<Highlight[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Observa mudanças no estado de autenticação
  useEffect(() => {
    if (!auth) {
      setUser(null);
      setIsLoading(false);
      return;
    }

    const unsub = auth.onAuthStateChanged((authUser) => {
      debugLog('Auth state changed:', authUser?.uid || 'no user');
      setUser(authUser);
      setIsLoading(false);
    });
    return unsub;
  }, []);

  const { colRef, lsKey, isUserRegistered } = useMemo(() => {
    // Wait for auth state to be determined
    if (user === undefined) {
      return { colRef: null, lsKey: '', isUserRegistered: false };
    }

    const isUserRegistered = user && !user.isAnonymous;
    const uid = user?.uid ?? "guest";
    const colRef = db && isUserRegistered ? collection(db, "grifos", uid, leiId) : null;
    const lsKey = `grifos-${uid}-${leiId}`;

    debugLog('Computed storage config:', { isUserRegistered, uid, lsKey, hasColRef: !!colRef });

    return { colRef, lsKey, isUserRegistered };
  }, [user, leiId]);

  // Efeito para carregar os dados da fonte correta (Firestore ou LocalStorage)
  useEffect(() => {
    // Don't load data until auth state is determined
    if (user === undefined || !lsKey) {
      return;
    }

    // Limpa os grifos atuais ao mudar de lei ou de usuário para evitar mostrar dados incorretos
    setHighlights([]);
    debugLog('Loading highlights for', { leiId, user: user?.uid || 'guest' });

    if (colRef) {
      // Se o usuário está logado, busca do Firestore
      console.debug('[useHighlights] Setting up Firestore listener for', leiId);
      const unsub = onSnapshot(colRef, (snap) => {
        const fromDb: Highlight[] = [];
        snap.forEach((d) => {
          const data = d.data();
          // Garante que apenas grifos no novo formato sejam carregados
          if (data.startContainerPath && data.endContainerPath) {
            fromDb.push({ id: d.id, ...data } as Highlight);
          }
        });
        console.debug('[useHighlights] Firestore highlights loaded for', leiId, fromDb);
        startTransition(() => setHighlights(fromDb));
      }, (error) => {
        console.error('[useHighlights] Erro ao carregar grifos do Firestore:', error);
        // Fallback to empty array on error
        startTransition(() => setHighlights([]));
      });
      return unsub; // Se desinscreve ao desmontar
    } else {
      // Se for anônimo ou Firebase não disponível, busca do localStorage
      console.debug('[useHighlights] Loading from localStorage with key:', lsKey);
      try {
        const raw = localStorage.getItem(lsKey);
        if (raw) {
          const parsed = JSON.parse(raw);
          // Validate the data structure
          if (Array.isArray(parsed)) {
            const validHighlights = parsed.filter(h =>
              h && h.id && h.startContainerPath && h.endContainerPath
            );
            console.debug('[useHighlights] localStorage highlights loaded for', leiId, validHighlights);
            startTransition(() => setHighlights(validHighlights));
          } else {
            console.warn('[useHighlights] Invalid localStorage data format, resetting');
            localStorage.removeItem(lsKey);
            startTransition(() => setHighlights([]));
          }
        } else {
          console.debug('[useHighlights] Nenhum highlight encontrado no localStorage para', leiId);
          startTransition(() => setHighlights([]));
        }
      } catch (e) {
        console.error('[useHighlights] Erro ao parsear highlights do localStorage:', e);
        // Clear corrupted data
        localStorage.removeItem(lsKey);
        startTransition(() => setHighlights([]));
      }
    }
  }, [leiId, user, colRef, lsKey]); // Re-executa quando a lei, usuário, colRef ou lsKey mudam

  // ===== Lógica de escrita diferida no localStorage (para usuários anônimos) =====
  const pendingJsonRef = useRef<string | null>(null);
  const flushTimeoutRef = useRef<number | null>(null);

  function scheduleLsFlush(json: string) {
    pendingJsonRef.current = json;
    if (flushTimeoutRef.current != null) return;

    const flush = () => {
      if (pendingJsonRef.current != null && lsKey) {
        try {
          localStorage.setItem(lsKey, pendingJsonRef.current);
          console.debug('[useHighlights] localStorage flush successful for key:', lsKey);
        } catch (error) {
          console.error('[useHighlights] localStorage flush failed:', error);
          // Try to clear some space and retry once
          try {
            // Clear old law caches to make space
            const keysToRemove: string[] = [];
            for (let i = 0; i < localStorage.length; i++) {
              const key = localStorage.key(i);
              if (key && key.startsWith('lawCache_')) {
                keysToRemove.push(key);
              }
            }
            keysToRemove.forEach(key => localStorage.removeItem(key));

            // Retry the save
            localStorage.setItem(lsKey, pendingJsonRef.current!);
            console.debug('[useHighlights] localStorage flush successful after cleanup');
          } catch (retryError) {
            console.error('[useHighlights] localStorage flush failed even after cleanup:', retryError);
          }
        }
        pendingJsonRef.current = null;
      }
      flushTimeoutRef.current = null;
    };

    // For critical data like highlights, use immediate flush instead of idle callback
    // to ensure data is saved before page unload
    flushTimeoutRef.current = window.setTimeout(flush, 100);
  }

  async function addHighlight(
    h: Omit<Highlight, "id">,
    container?: Element,
    lawContent?: string
  ) {
    const { startContainerPath, endContainerPath } = h;
    if (!startContainerPath || !endContainerPath) {
      console.warn("[useHighlights] Tentativa de salvar grifo em formato antigo. Ignorando.");
      return;
    }

    if (!lsKey) {
      console.error("[useHighlights] Cannot save highlight: no storage key available");
      return;
    }

    // Enriquece o highlight com dados de persistência se container estiver disponível
    let enrichedHighlight = h;
    if (container) {
      try {
        const startNode = getNodeFromPath(h.startContainerPath, container);
        const endNode = getNodeFromPath(h.endContainerPath, container);

        if (startNode && endNode) {
          enrichedHighlight = enrichHighlight(h, startNode, endNode, container, lawContent);
          debugLog('Highlight enriquecido com dados de persistência');
        }
      } catch (error) {
        debugLog('Erro ao enriquecer highlight, usando dados originais:', error);
      }
    }

    const provisionalId = generateUUID();
    const newHighlight = { ...enrichedHighlight, id: provisionalId };

    console.debug('[useHighlights] Adding highlight:', newHighlight);

    // 1. Atualização otimista da UI
    startTransition(() => {
      setHighlights(prev => {
        const updated = [...prev, newHighlight];
        if (!colRef) {
          // For localStorage users, immediately schedule save
          scheduleLsFlush(JSON.stringify(updated));
        }
        return updated;
      });
    });

    if (!colRef) {
      console.debug('[useHighlights] Highlight saved to localStorage');
      return;
    }

    // 2. Persistência no Firestore
    try {
      console.debug('[useHighlights] Saving to Firestore...');
      const docRef = await addDoc(colRef, enrichedHighlight);
      console.debug('[useHighlights] Firestore save successful, ID:', docRef.id);

      // Atualiza o ID provisório pelo ID final do DB
      startTransition(() => {
        setHighlights(prev => prev.map(hl =>
          hl.id === provisionalId ? { ...hl, id: docRef.id } : hl
        ));
      });
    } catch (err) {
      console.error("[useHighlights] Falha ao salvar grifo no Firestore:", err);

      // Em caso de erro, reverte a atualização otimista
      startTransition(() => {
        setHighlights(prev => prev.filter(hl => hl.id !== provisionalId));
      });

      // Show user-friendly error
      throw new Error("Não foi possível salvar o grifo. Verifique sua conexão.");
    }
  }

  async function removeHighlight(id: string) {
    console.debug('[useHighlights] Removing highlight:', id);

    // Optimistic UI update first (for both localStorage and Firestore)
    const previousHighlights = highlights;
    startTransition(() => {
      setHighlights((prev) => prev.filter((h) => h.id !== id));
    });

    if (!colRef) {
      // localStorage mode - persist the change immediately
      try {
        const updated = previousHighlights.filter((h) => h.id !== id);
        localStorage.setItem(lsKey, JSON.stringify(updated));
        console.debug('[useHighlights] Highlight removed from localStorage immediately');
      } catch (error) {
        console.error('[useHighlights] Failed to persist highlight removal to localStorage:', error);
        // Revert the optimistic update
        startTransition(() => {
          setHighlights(previousHighlights);
        });
        throw new Error("Não foi possível remover o grifo do armazenamento local.");
      }
      return;
    }

    // Firestore mode
    try {
      await deleteDoc(doc(colRef, id));
      console.debug('[useHighlights] Highlight removed from Firestore');
    } catch (err) {
      console.error('[useHighlights] Failed to remove highlight from Firestore:', err);
      // Revert the optimistic update
      startTransition(() => {
        setHighlights(previousHighlights);
      });
      throw new Error("Não foi possível remover o grifo. Verifique sua conexão.");
    }
  }

  // Emergency save on page unload and cleanup
  useEffect(() => {
    const handleBeforeUnload = () => {
      // Force immediate save of any pending data
      if (pendingJsonRef.current && lsKey) {
        try {
          localStorage.setItem(lsKey, pendingJsonRef.current);
          console.debug('[useHighlights] Emergency save on page unload');
        } catch (error) {
          console.error('[useHighlights] Emergency save failed:', error);
        }
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      // Cleanup
      window.removeEventListener('beforeunload', handleBeforeUnload);

      if (flushTimeoutRef.current != null) {
        window.clearTimeout(flushTimeoutRef.current);
        flushTimeoutRef.current = null;
      }

      // Final emergency save
      handleBeforeUnload();
    };
  }, [lsKey]);

  // ===== Função auxiliar para migrar highlights no Firestore =====
  const migrateHighlightsInFirestore = async (migrationResult: any, colRef: any) => {
    if (!db) return;

    try {
      const batch = writeBatch(db);

      // Atualiza highlights válidos (apenas versão e lastValidated)
      for (const highlight of migrationResult.validHighlights) {
        const docRef = doc(colRef, highlight.id);
        batch.update(docRef, {
          lawVersion: highlight.lawVersion,
          lastValidated: highlight.lastValidated
        });
      }

      // Remove highlights órfãos
      for (const highlight of migrationResult.orphanedHighlights) {
        const docRef = doc(colRef, highlight.id);
        batch.delete(docRef);
      }

      // Para highlights migrados, remove o antigo e adiciona o novo
      for (const highlight of migrationResult.migratedHighlights) {
        // Remove o documento antigo
        const oldDocRef = doc(colRef, highlight.id);
        batch.delete(oldDocRef);

        // Adiciona o novo documento (sem ID para que o Firestore gere um novo)
        const newDocRef = doc(colRef);
        const { id, ...highlightData } = highlight;
        batch.set(newDocRef, highlightData);
      }

      await batch.commit();
      debugLog('Migração no Firestore concluída com sucesso');
    } catch (error) {
      console.error('[useHighlights] Erro durante migração no Firestore:', error);
      throw error;
    }
  };

  // ===== Função para migrar highlights quando lei é atualizada =====
  const migrateHighlightsIfNeeded = async (
    container: Element,
    lawContent: string,
    lawVersion: string
  ) => {
    if (!highlights.length) return;

    // Verifica se há highlights que precisam ser migrados
    const needsMigration = detectLawUpdate(highlights, lawVersion);

    if (!needsMigration) {
      debugLog('Nenhuma migração necessária');
      return;
    }

    debugLog('Iniciando migração de highlights para nova versão da lei');

    try {
      const migrationResult = migrateHighlights(highlights, container, lawContent, lawVersion);

      // Combina highlights válidos e migrados
      const updatedHighlights = [
        ...migrationResult.validHighlights,
        ...migrationResult.migratedHighlights
      ];

      // Remove highlights órfãos antigos
      const cleanedHighlights = cleanupOrphanedHighlights(updatedHighlights);

      // Atualiza o estado
      startTransition(() => {
        setHighlights(cleanedHighlights);
      });

      // Salva no storage apropriado
      if (colRef) {
        // Para Firestore, usa batch update
        await migrateHighlightsInFirestore(migrationResult, colRef);
      } else {
        // Para localStorage, salva imediatamente
        scheduleLsFlush(JSON.stringify(cleanedHighlights));
      }

      debugLog('Migração concluída:', migrationResult.stats);

      // Notifica sobre highlights órfãos se houver
      if (migrationResult.orphanedHighlights.length > 0) {
        console.warn(
          `[useHighlights] ${migrationResult.orphanedHighlights.length} highlights não puderam ser migrados e foram removidos`
        );
      }
    } catch (error) {
      console.error('[useHighlights] Erro durante migração de highlights:', error);
    }
  };

  // ===== Função para validar integridade dos highlights =====
  const validateHighlightsIntegrity = (container: Element) => {
    if (!highlights.length) return { valid: 0, invalid: 0, total: 0 };

    let valid = 0;
    let invalid = 0;

    for (const highlight of highlights) {
      if (validateHighlight(highlight, container)) {
        valid++;
      } else {
        invalid++;
        debugLog(`Highlight inválido encontrado: ${highlight.id}`);
      }
    }

    const result = { valid, invalid, total: highlights.length };
    debugLog('Validação de integridade concluída:', result);
    return result;
  };

  // ===== Função para gerar relatório de saúde dos highlights =====
  const getHighlightsHealthReport = (container: Element) => {
    return generateHighlightHealthReport(highlights, container);
  };

  // ===== Função para limpar highlights órfãos =====
  const cleanupOrphanedHighlightsManual = () => {
    const cleanedHighlights = cleanupOrphanedHighlights(highlights);

    if (cleanedHighlights.length !== highlights.length) {
      const removedCount = highlights.length - cleanedHighlights.length;
      debugLog(`Removidos ${removedCount} highlights órfãos antigos`);

      startTransition(() => {
        setHighlights(cleanedHighlights);
      });

      // Salva no storage apropriado
      if (!colRef) {
        scheduleLsFlush(JSON.stringify(cleanedHighlights));
      }
      // Para Firestore, seria necessário implementar remoção em lote
    }
  };

  return {
    highlights,
    addHighlight,
    removeHighlight,
    migrateHighlightsIfNeeded,
    validateHighlightsIntegrity,
    getHighlightsHealthReport,
    cleanupOrphanedHighlightsManual,
    isLoading: user === undefined,
    isAuthenticated: !!user && !user.isAnonymous
  };
}