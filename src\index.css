/* Estilos globais adicionados */
@layer base {
  body {
    @apply font-["Inter",_sans-serif] bg-gray-50 dark:bg-gray-950;
  }

  ::selection {
    @apply bg-primary text-white;
  }

  /* Scrollbar estilizado */
  ::-webkit-scrollbar {
    width: 12px;
    height: 12px;
  }
  ::-webkit-scrollbar-thumb {
    background-color: #7f5af0; /* roxo primário fixo para visibilidade */
    border-radius: 4px;
  }
  /* Para tema claro deixar a barra mais visível */
  html:not(.dark) ::-webkit-scrollbar-thumb {
    background-color: #7f5af0;
  }
  html:not(.dark) ::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.08);
  }

  html.dark ::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.25);
  }
}

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
  .animate-fadeIn {
    animation: fadeIn 0.15s ease-out both;
  }

  /* Slide-up animation */
  @keyframes slideUp {
    from { opacity:0; transform: translateY(8px); }
    to { opacity:1; transform: translateY(0); }
  }
  .animate-slideUp { animation: slideUp 0.25s cubic-bezier(0.16, 1, 0.3, 1) both; }

  /* Glass card utility */
  .glass {
    /* @apply bg-white/10 backdrop-blur-md border border-white/20 shadow-lg rounded-2xl; */
  }
}

/* ===== Temas adicionais ===== */
html.theme-sepia body {
  background: #f4ecd8;
  color: #5b4636;
}

html.theme-sepia body::before {
  background: radial-gradient(circle at 10% 20%, #c19a6b 0%, transparent 40%), radial-gradient(circle at 90% 80%, #b5835a 0%, transparent 40%);
}

html.theme-high-contrast body {
  background: #000;
  color: #fff;
}

html.theme-high-contrast ::selection {
  background: #fff;
  color: #000;
}

html.theme-high-contrast body::before,
html.theme-high-contrast body::after {
  display: none; /* remove efeitos visuais */
}

/* ===== Ocean Theme ===== */
html.theme-ocean body {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  color: #e2e8f0;
}

html.theme-ocean body::before {
  background: radial-gradient(circle at 20% 30%, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
              radial-gradient(circle at 80% 70%, rgba(14, 165, 233, 0.1) 0%, transparent 50%);
}

html.theme-ocean ::selection {
  background: #0ea5e9;
  color: #f8fafc;
}

html.theme-ocean ::-webkit-scrollbar-thumb {
  background-color: #0ea5e9;
}

html.theme-ocean ::-webkit-scrollbar-track {
  background: rgba(14, 165, 233, 0.1);
}

/* ===== Forest Theme ===== */
html.theme-forest body {
  background: linear-gradient(135deg, #14532d 0%, #166534 50%, #15803d 100%);
  color: #f0fdf4;
}

html.theme-forest body::before {
  background: radial-gradient(circle at 25% 25%, rgba(34, 197, 94, 0.12) 0%, transparent 50%),
              radial-gradient(circle at 75% 75%, rgba(22, 163, 74, 0.08) 0%, transparent 50%);
}

html.theme-forest ::selection {
  background: #22c55e;
  color: #14532d;
}

html.theme-forest ::-webkit-scrollbar-thumb {
  background-color: #22c55e;
}

html.theme-forest ::-webkit-scrollbar-track {
  background: rgba(34, 197, 94, 0.1);
}

/* ===== Sunset Theme ===== */
html.theme-sunset body {
  background: linear-gradient(135deg, #7c2d12 0%, #ea580c 50%, #fb923c 100%);
  color: #fef7ed;
}

html.theme-sunset body::before {
  background: radial-gradient(circle at 30% 20%, rgba(251, 146, 60, 0.15) 0%, transparent 50%),
              radial-gradient(circle at 70% 80%, rgba(234, 88, 12, 0.1) 0%, transparent 50%);
}

html.theme-sunset ::selection {
  background: #fb923c;
  color: #7c2d12;
}

html.theme-sunset ::-webkit-scrollbar-thumb {
  background-color: #fb923c;
}

html.theme-sunset ::-webkit-scrollbar-track {
  background: rgba(251, 146, 60, 0.1);
}

/* ===== Theme-specific law content adjustments ===== */

/* Ocean theme law content */
html.theme-ocean #lei-container p.note {
  color: #7dd3fc !important;
}
html.theme-ocean #lei-container p.note .lei-ref {
  color: #38bdf8 !important;
}
html.theme-ocean #lei-container a {
  color: #7dd3fc !important;
}
html.theme-ocean #lei-container a:hover {
  color: #bae6fd !important;
}
html.theme-ocean .art-ref {
  color: #7dd3fc !important;
}

/* Forest theme law content */
html.theme-forest #lei-container p.note {
  color: #86efac !important;
}
html.theme-forest #lei-container p.note .lei-ref {
  color: #4ade80 !important;
}
html.theme-forest #lei-container a {
  color: #86efac !important;
}
html.theme-forest #lei-container a:hover {
  color: #bbf7d0 !important;
}
html.theme-forest .art-ref {
  color: #86efac !important;
}

/* Sunset theme law content */
html.theme-sunset #lei-container p.note {
  color: #fdba74 !important;
}
html.theme-sunset #lei-container p.note .lei-ref {
  color: #fb923c !important;
}
html.theme-sunset #lei-container a {
  color: #fdba74 !important;
}
html.theme-sunset #lei-container a:hover {
  color: #fed7aa !important;
}
html.theme-sunset .art-ref {
  color: #fdba74 !important;
}

/* ===== Modal theme support ===== */

/* Default light theme modal */
.modal-bg {
  background: #ffffff;
  color: #111827;
}

/* Dark theme modal */
html.dark .modal-bg,
html.theme-dark .modal-bg {
  background: #262626;
  color: #f9fafb;
}

/* Sepia theme modal */
html.theme-sepia .modal-bg {
  background: #f7f1e8;
  color: #5b4636;
  border: 1px solid #d4c4a8;
}

/* Ocean theme modal */
html.theme-ocean .modal-bg {
  background: #1e293b;
  color: #e2e8f0;
  border: 1px solid #334155;
}

/* Forest theme modal */
html.theme-forest .modal-bg {
  background: #166534;
  color: #f0fdf4;
  border: 1px solid #15803d;
}

/* Sunset theme modal */
html.theme-sunset .modal-bg {
  background: #ea580c;
  color: #fef7ed;
  border: 1px solid #fb923c;
}

/* ===== Color Palette theme support ===== */

/* Default light theme palette */
.palette-bg {
  background: rgba(38, 38, 38, 0.9);
}

.palette-popup-bg {
  background: #1a1a1a;
}

/* Dark theme palette */
html.dark .palette-bg,
html.theme-dark .palette-bg {
  background: rgba(38, 38, 38, 0.9);
}

html.dark .palette-popup-bg,
html.theme-dark .palette-popup-bg {
  background: #1a1a1a;
}

/* Sepia theme palette */
html.theme-sepia .palette-bg {
  background: rgba(91, 70, 54, 0.9);
}

html.theme-sepia .palette-popup-bg {
  background: #4a3728;
}

/* Ocean theme palette */
html.theme-ocean .palette-bg {
  background: rgba(30, 41, 59, 0.9);
}

html.theme-ocean .palette-popup-bg {
  background: #0f172a;
}

/* Forest theme palette */
html.theme-forest .palette-bg {
  background: rgba(22, 101, 52, 0.9);
}

html.theme-forest .palette-popup-bg {
  background: #14532d;
}

/* Sunset theme palette */
html.theme-sunset .palette-bg {
  background: rgba(234, 88, 12, 0.9);
}

html.theme-sunset .palette-popup-bg {
  background: #7c2d12;
}

/* ===== Card theme support ===== */

/* Default light theme card */
.card-bg {
  background: #ffffff;
  color: #1f2937;
  border: 1px solid #d1d5db;
}

/* Dark theme card */
html.dark .card-bg,
html.theme-dark .card-bg {
  background: rgba(255, 255, 255, 0.05);
  color: #f9fafb;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Sepia theme card */
html.theme-sepia .card-bg {
  background: #faf7f0;
  color: #5b4636;
  border: 1px solid #e5d5b7;
}

/* Ocean theme card */
html.theme-ocean .card-bg {
  background: rgba(226, 232, 240, 0.05);
  color: #e2e8f0;
  border: 1px solid rgba(226, 232, 240, 0.1);
}

/* Forest theme card */
html.theme-forest .card-bg {
  background: rgba(240, 253, 244, 0.05);
  color: #f0fdf4;
  border: 1px solid rgba(240, 253, 244, 0.1);
}

/* Sunset theme card */
html.theme-sunset .card-bg {
  background: rgba(254, 247, 237, 0.05);
  color: #fef7ed;
  border: 1px solid rgba(254, 247, 237, 0.1);
}

/* ===== Profile Section theme support ===== */

/* Default light theme profile sections */
.profile-section-bg {
  background: #ffffff !important;
  color: #1f2937 !important;
  border: 1px solid #d1d5db !important;
}

/* Dark theme profile sections */
html.dark .profile-section-bg,
html.theme-dark .profile-section-bg {
  background: rgba(255, 255, 255, 0.05) !important;
  color: #f9fafb !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* Sepia theme profile sections */
html.theme-sepia .profile-section-bg {
  background: #faf7f0 !important;
  color: #5b4636 !important;
  border: 1px solid #e5d5b7 !important;
}

/* Ocean theme profile sections */
html.theme-ocean .profile-section-bg {
  background: rgba(226, 232, 240, 0.05) !important;
  color: #e2e8f0 !important;
  border: 1px solid rgba(226, 232, 240, 0.1) !important;
}

/* Forest theme profile sections */
html.theme-forest .profile-section-bg {
  background: rgba(240, 253, 244, 0.05) !important;
  color: #f0fdf4 !important;
  border: 1px solid rgba(240, 253, 244, 0.1) !important;
}

/* Sunset theme profile sections */
html.theme-sunset .profile-section-bg {
  background: rgba(254, 247, 237, 0.05) !important;
  color: #fef7ed !important;
  border: 1px solid rgba(254, 247, 237, 0.1) !important;
}

/* ===== Profile Inner Section theme support ===== */

/* Default light theme profile inner sections */
.profile-inner-bg {
  background: #f9fafb !important;
  color: #1f2937 !important;
}

/* Dark theme profile inner sections */
html.dark .profile-inner-bg,
html.theme-dark .profile-inner-bg {
  background: rgba(255, 255, 255, 0.1) !important;
  color: #f9fafb !important;
}

/* Sepia theme profile inner sections */
html.theme-sepia .profile-inner-bg {
  background: #f0ebe0 !important;
  color: #5b4636 !important;
}

/* Ocean theme profile inner sections */
html.theme-ocean .profile-inner-bg {
  background: rgba(226, 232, 240, 0.1) !important;
  color: #e2e8f0 !important;
}

/* Forest theme profile inner sections */
html.theme-forest .profile-inner-bg {
  background: rgba(240, 253, 244, 0.1) !important;
  color: #f0fdf4 !important;
}

/* Sunset theme profile inner sections */
html.theme-sunset .profile-inner-bg {
  background: rgba(254, 247, 237, 0.1) !important;
  color: #fef7ed !important;
}