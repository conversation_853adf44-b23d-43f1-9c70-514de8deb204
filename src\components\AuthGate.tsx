import { ReactNode, useEffect, useState } from "react";
import { onAuthStateChanged, User } from "firebase/auth";
import { auth } from "../firebase";
import { useNavigate } from "react-router-dom";

interface Props {
  children: ReactNode;
}

export function AuthGate({ children }: Props) {
  const [user, setUser] = useState<User | null | undefined>(undefined);
  const navigate = useNavigate();

  useEffect(() => {
    if (!auth) {
      setUser(null);
      return;
    }
    return onAuthStateChanged(auth, (u) => setUser(u));
  }, []);

  useEffect(() => {
    if (user === null && typeof localStorage !== 'undefined' && localStorage.getItem('guest_mode') !== '1') {
      navigate('/login', { replace: true });
    }
  }, [user, navigate]);

  if (user === undefined) {
    return (
      <div className="flex h-screen items-center justify-center text-xl">
        Carregando...
      </div>
    );
  }

  return (
    <div className="relative min-h-screen">
      {children}
      {user && (
        <div className="fixed bottom-2 left-2 text-xs opacity-70 select-none">
          UID: {user.uid.substring(0, 6)}
        </div>
      )}
    </div>
  );
} 