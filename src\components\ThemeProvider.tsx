import React, { createContext, useContext, useEffect, useState } from "react";

// Suporte a múltiplos estilos visuais
export type Theme = "light" | "dark" | "sepia" | "ocean" | "forest" | "sunset";

interface ThemeContextValue {
  theme: Theme;
  toggle: () => void;
  setTheme: (t: Theme) => void;
}

// Contexto com valores padrão
const ThemeContext = createContext<ThemeContextValue>({
  theme: "dark",
  // no-op placeholders
  /* eslint-disable-next-line @typescript-eslint/no-empty-function */
  toggle: () => {},
  /* eslint-disable-next-line @typescript-eslint/no-empty-function */
  setTheme: () => {},
});

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  // Determina o tema inicial: localStorage > preferência do sistema > dark (default)
  const [theme, setTheme] = useState<Theme>(() => {
    if (typeof window === "undefined") return "dark";
    const stored = window.localStorage.getItem("theme") as Theme | null;
    if (stored) return stored;
    const prefersDark = window.matchMedia("(prefers-color-scheme: dark)").matches;
    return prefersDark ? "dark" : "dark"; // Default to dark theme as per user preference
  });

  // Sempre que o tema muda, atualiza <html> e persiste em localStorage
  useEffect(() => {
    if (typeof document === "undefined") return;
    const root = document.documentElement;

    // Remove classes antigas
    root.classList.remove(
      "dark",
      "theme-light",
      "theme-dark",
      "theme-sepia",
      "theme-ocean",
      "theme-forest",
      "theme-sunset"
    );

    // Aplica classe genérica do tema
    root.classList.add(`theme-${theme}`);

    // Modo dark continua usando classe 'dark' para Tailwind quando pertinente
    if (theme === "dark") {
      root.classList.add("dark");
    }

    window.localStorage.setItem("theme", theme);
  }, [theme]);

  const toggle = () =>
    setTheme((t) => {
      switch (t) {
        case "light": return "dark";
        case "dark": return "sepia";
        case "sepia": return "ocean";
        case "ocean": return "forest";
        case "forest": return "sunset";
        case "sunset": return "light";
        default: return "dark";
      }
    });

  const setThemeExplicit = (t: Theme) => setTheme(t);

  return (
    <ThemeContext.Provider value={{ theme, toggle, setTheme: setThemeExplicit }}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  return useContext(ThemeContext);
} 