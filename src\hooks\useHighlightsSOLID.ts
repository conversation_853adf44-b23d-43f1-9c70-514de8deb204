import { useState, useEffect, useRef, useMemo, startTransition } from "react";
import { auth, db } from "../firebase";
import { generateUUID } from "../utils/uuid";
import type { Highlight } from "./useHighlights";
import { 
  IHighlightStorage, 
  HighlightStorageFactory 
} from "../abstractions/HighlightStorage";
import { 
  container, 
  ServiceKeys, 
  useService,
  type ILogger 
} from "../abstractions/ServiceContainer";

/**
 * SOLID-compliant hook for managing highlights
 * Follows Dependency Inversion Principle by depending on abstractions
 */
export function useHighlightsSOLID(leiId: string) {
  const [highlights, setHighlights] = useState<Highlight[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState<any>(undefined);
  
  // Depend on abstraction, not concrete implementation
  const logger = useService<ILogger>(ServiceKeys.LOGGER);
  const storageRef = useRef<IHighlightStorage | null>(null);
  const unsubscribeRef = useRef<(() => void) | null>(null);

  // Auth state management
  useEffect(() => {
    const unsub = auth.onAuthStateChanged((authUser) => {
      logger.debug('Auth state changed:', authUser?.uid || 'no user');
      setUser(authUser);
      setIsLoading(false);
    });

    return unsub;
  }, [logger]);

  // Storage configuration based on user state
  const storageConfig = useMemo(() => {
    if (user === undefined) {
      return null;
    }

    const isUserRegistered = user && !user.isAnonymous;
    
    if (isUserRegistered && db) {
      return {
        type: 'firestore' as const,
        options: { db, userId: user.uid }
      };
    } else {
      return {
        type: 'localStorage' as const,
        options: {}
      };
    }
  }, [user]);

  // Initialize storage and load highlights
  useEffect(() => {
    if (!storageConfig || !leiId) {
      return;
    }

    // Clean up previous subscription
    if (unsubscribeRef.current) {
      unsubscribeRef.current();
      unsubscribeRef.current = null;
    }

    try {
      // Create storage instance using factory (follows OCP)
      const storage = HighlightStorageFactory.create(
        storageConfig.type, 
        storageConfig.options
      );
      storageRef.current = storage;

      logger.debug('Setting up highlight storage:', storageConfig.type);

      // Subscribe to changes
      const unsubscribe = storage.subscribe(leiId, (loadedHighlights) => {
        logger.debug('Highlights loaded:', loadedHighlights.length);
        startTransition(() => setHighlights(loadedHighlights));
      });

      unsubscribeRef.current = unsubscribe;

      // Initial load
      storage.load(leiId).then((loadedHighlights) => {
        logger.debug('Initial highlights loaded:', loadedHighlights.length);
        startTransition(() => setHighlights(loadedHighlights));
      }).catch((error) => {
        logger.error('Error loading initial highlights:', error);
        startTransition(() => setHighlights([]));
      });

    } catch (error) {
      logger.error('Error setting up highlight storage:', error);
      startTransition(() => setHighlights([]));
    }

    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
        unsubscribeRef.current = null;
      }
    };
  }, [leiId, storageConfig, logger]);

  // Add highlight function
  const addHighlight = async (h: Omit<Highlight, 'id'>) => {
    if (!storageRef.current) {
      logger.warn('No storage available for adding highlight');
      return;
    }

    const provisionalId = generateUUID();
    const newHighlight = { ...h, id: provisionalId };

    logger.debug('Adding highlight:', newHighlight.id);

    // Optimistic update
    startTransition(() => {
      setHighlights(prev => [...prev, newHighlight]);
    });

    try {
      await storageRef.current.add(leiId, newHighlight);
      logger.debug('Highlight added successfully:', newHighlight.id);
    } catch (error) {
      logger.error('Error adding highlight:', error);
      
      // Rollback optimistic update
      startTransition(() => {
        setHighlights(prev => prev.filter(highlight => highlight.id !== provisionalId));
      });
      
      throw error;
    }
  };

  // Remove highlight function
  const removeHighlight = async (id: string) => {
    if (!storageRef.current) {
      logger.warn('No storage available for removing highlight');
      return;
    }

    logger.debug('Removing highlight:', id);

    // Store original state for rollback
    const originalHighlights = highlights;

    // Optimistic update
    startTransition(() => {
      setHighlights(prev => prev.filter(h => h.id !== id));
    });

    try {
      await storageRef.current.remove(leiId, id);
      logger.debug('Highlight removed successfully:', id);
    } catch (error) {
      logger.error('Error removing highlight:', error);
      
      // Rollback optimistic update
      startTransition(() => {
        setHighlights(originalHighlights);
      });
      
      throw error;
    }
  };

  // Export/Import functions
  const exportHighlights = () => {
    const dataStr = JSON.stringify(highlights, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
    
    const exportFileDefaultName = `grifos-${leiId}-${new Date().toISOString().split('T')[0]}.json`;
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
    
    logger.info('Highlights exported:', highlights.length);
  };

  const importHighlights = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !storageRef.current) return;

    try {
      const text = await file.text();
      const importedHighlights: Highlight[] = JSON.parse(text);
      
      if (!Array.isArray(importedHighlights)) {
        throw new Error('Invalid file format');
      }

      // Validate highlights structure
      const validHighlights = importedHighlights.filter(h => 
        h && h.id && h.startContainerPath && h.endContainerPath && h.text && h.color
      );

      logger.info('Importing highlights:', validHighlights.length);

      // Add each highlight
      for (const highlight of validHighlights) {
        await storageRef.current.add(leiId, { ...highlight, id: generateUUID() });
      }

      logger.info('Highlights imported successfully');
    } catch (error) {
      logger.error('Error importing highlights:', error);
      throw error;
    } finally {
      // Clear the input
      event.target.value = '';
    }
  };

  return {
    highlights,
    addHighlight,
    removeHighlight,
    exportHighlights,
    importHighlights,
    isLoading: isLoading || user === undefined,
    isAuthenticated: user && !user.isAnonymous,
  };
}
