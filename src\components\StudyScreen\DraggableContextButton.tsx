import React, { useState, useRef, useEffect } from 'react';

function abbreviate(str: string) {
  if (str.length < 50) return str;
  const parts = str.split(/[·•]/).map(s => s.trim());
  if (parts.length <= 3) return str;

  const lastPart = parts.pop() || '';
  const firstPart = parts.shift() || '';

  return `${firstPart} · ... · ${lastPart}`;
}

interface DraggableContextButtonProps {
  contextoCabecalho: string;
  onHide: () => void;
}

export function DraggableContextButton({ contextoCabecalho, onHide }: DraggableContextButtonProps) {
  const [position, setPosition] = useState({ x: window.innerWidth - 320, y: 20 }); // Position in top-right
  const [isDragging, setIsDragging] = useState(false);
  const [scrolling, setScrolling] = useState(false);
  const dragRef = useRef<HTMLDivElement | null>(null);
  const offsetRef = useRef({ x: 0, y: 0 });
    const scrollTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  useEffect(() => {
    const handleScroll = () => {
      setScrolling(true);
      if (scrollTimeoutRef.current) clearTimeout(scrollTimeoutRef.current);
      scrollTimeoutRef.current = setTimeout(() => setScrolling(false), 200);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => {
      window.removeEventListener('scroll', handleScroll);
      if (scrollTimeoutRef.current) clearTimeout(scrollTimeoutRef.current);
    };
  }, []);

  const onMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!dragRef.current) return;
    setIsDragging(true);
    const rect = dragRef.current.getBoundingClientRect();
    offsetRef.current = {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
    };
    e.preventDefault();
  };

  const onMouseMove = (e: MouseEvent) => {
    if (!isDragging || !dragRef.current) return;
    let newX = e.clientX - offsetRef.current.x;
    let newY = e.clientY - offsetRef.current.y;

    const bounds = dragRef.current.parentElement?.getBoundingClientRect();
    if (bounds) {
      newX = Math.max(0, Math.min(newX, bounds.width - dragRef.current.offsetWidth));
      newY = Math.max(0, Math.min(newY, bounds.height - dragRef.current.offsetHeight));
    }

    setPosition({ x: newX, y: newY });
  };

  const onMouseUp = () => {
    setIsDragging(false);
  };

  useEffect(() => {
    if (isDragging) {
      window.addEventListener('mousemove', onMouseMove);
      window.addEventListener('mouseup', onMouseUp);
    } else {
      window.removeEventListener('mousemove', onMouseMove);
      window.removeEventListener('mouseup', onMouseUp);
    }

    return () => {
      window.removeEventListener('mousemove', onMouseMove);
      window.removeEventListener('mouseup', onMouseUp);
    };
  }, [isDragging]);

  const onScrollClick = (e: React.MouseEvent<HTMLDivElement>) => {
    const sentinel = document.getElementById('sentinel-end');
    if (sentinel) {
      sentinel.dispatchEvent(new CustomEvent('manual-intersect'));
    }
    e.stopPropagation();
  };

  return (
    <div
      ref={dragRef}
      className="draggable-context-button fixed z-50 bg-gray-800 bg-opacity-90 backdrop-blur-sm rounded-lg shadow-lg select-none border border-gray-600"
      style={{ left: `${position.x}px`, top: `${position.y}px`, touchAction: 'none' }}
    >
      {/* Header with drag handle and close button */}
      <div
        className="flex items-center justify-between p-2 cursor-grab border-b border-gray-600"
        onMouseDown={onMouseDown}
      >
        <span className="text-xs text-gray-300 font-medium">Contexto Hierárquico</span>
        <button
          onClick={onHide}
          className="text-gray-400 hover:text-white text-sm w-5 h-5 flex items-center justify-center rounded hover:bg-gray-700"
          title="Fechar"
        >
          ×
        </button>
      </div>

      {/* Content */}
      <div
        className="context-content flex items-center gap-1.5 overflow-hidden p-2"
        onClick={onScrollClick}
        tabIndex={-1}
      >
        {contextoCabecalho ? (
          abbreviate(contextoCabecalho).split(/\s*[·•]\s*/).map((part, idx, arr) => {
            const partLower = part.trim().toLowerCase();
            let chipClass = "text-white font-medium rounded-md px-2 py-1 text-xs shadow-sm whitespace-nowrap flex-shrink-0 transition-colors";

            if (partLower.startsWith('l.')) chipClass += " bg-red-600 hover:bg-red-500";
            else if (partLower.startsWith('t.')) chipClass += " bg-blue-600 hover:bg-blue-500";
            else if (partLower.startsWith('c.')) chipClass += " bg-green-600 hover:bg-green-500";
            else if (partLower.startsWith('s.')) chipClass += " bg-amber-600 hover:bg-amber-500";
            else chipClass += " bg-gray-600 hover:bg-gray-500";

            return (
              <React.Fragment key={idx}>
                <span className={chipClass} style={{ userSelect: 'none', pointerEvents: scrolling ? 'none' : 'auto' }}>
                  {part.trim()}
                </span>
                {idx < arr.length - 1 && (
                  <span className="text-gray-400 text-xs">→</span>
                )}
              </React.Fragment>
            );
          })
        ) : (
          <span className="text-gray-400 text-xs italic">Carregando contexto...</span>
        )}
      </div>
    </div>
  );
}
