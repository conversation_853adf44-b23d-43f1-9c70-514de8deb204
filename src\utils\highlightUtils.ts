import type { Highlight } from "../hooks/useHighlights";
import { getNodeFromPathWithFallback, getNodeFromPathWithAdvancedFallback, hasOverlappingHighlight, applySafeHighlight } from "./nodeUtils";

// Debug logging utility (disabled to prevent browser debug issues)
const debugLog = (message: string, ...args: any[]) => {
  // Disabled to prevent browser debug issues
  // if (import.meta.env.DEV) {
  //   console.debug(`[HighlightUtils] ${message}`, ...args);
  // }
};

/**
 * Aplica um único highlight usando nó único (otimização para highlights simples)
 */
export function applySingleNodeHighlight(
  h: Highlight,
  targetContainer: Element,
  onRemove?: (id: string) => void
): boolean {
  // Primeiro tenta o sistema avançado de fallback
  let startNode = getNodeFromPathWithAdvancedFallback(h, targetContainer);

  // Se não encontrou, tenta o sistema original
  if (!startNode) {
    startNode = getNodeFromPathWithFallback(h.startContainerPath, targetContainer, h.text, true);
  }

  if (!startNode || startNode.nodeType !== Node.TEXT_NODE) {
    debugLog(`Grifo ${h.id}: Nó não encontrado ou não é texto`);
    return false;
  }

  const textContent = startNode.textContent || '';
  
  // Busca o texto do highlight no nó
  let textIndex = textContent.indexOf(h.text);
  
  // Se não encontrou diretamente, tenta por palavras-chave
  if (textIndex === -1) {
    const words = h.text.split(' ').filter(w => w.length > 2);
    for (const word of words) {
      textIndex = textContent.indexOf(word);
      if (textIndex !== -1) {
        debugLog(`Grifo ${h.id}: Encontrado por palavra-chave: "${word}"`);
        break;
      }
    }
  }
  
  if (textIndex === -1) {
    // console.warn(`Grifo ${h.id}: Texto não encontrado no nó`);
    return false;
  }

  // Calcula offsets baseados na posição do texto
  const startOffset = textIndex;
  const endOffset = Math.min(textIndex + h.text.length, textContent.length);
  
  // Validação de offsets
  if (startOffset >= textContent.length || endOffset > textContent.length) {
    // console.warn(`Grifo ${h.id}: Offsets calculados são inválidos`);
    return false;
  }

  try {
    const range = document.createRange();
    range.setStart(startNode, startOffset);
    range.setEnd(startNode, endOffset);
    
    if (range.collapsed || range.toString().trim().length === 0) {
      // console.warn(`Grifo ${h.id}: Range calculado está vazio`);
      range.detach();
      return false;
    }

    const mark = document.createElement('mark');
    mark.style.backgroundColor = h.color;
    mark.dataset.grifo = h.id;
    mark.className = 'highlight-mark';

    // Adiciona evento de clique para remoção direta
    if (onRemove) {
      // Função de remoção
      const handleRemove = (e: Event) => {
        e.preventDefault();
        e.stopPropagation();
        e.stopImmediatePropagation();
        console.log('🗑️ Removendo grifo:', h.id); // Log sempre visível para debug
        onRemove(h.id);
      };

      // Múltiplos eventos para garantir que funcione
      mark.addEventListener('mousedown', (e) => {
        if (e.button === 0) { // Só clique esquerdo
          handleRemove(e);
        }
      }, { capture: true });

      mark.addEventListener('click', handleRemove, { capture: true });
      mark.addEventListener('touchstart', handleRemove, { capture: true });

      mark.style.cursor = 'pointer';
      mark.title = 'Clique para remover o grifo';

      // Adiciona atributo para facilitar debug
      mark.setAttribute('data-removable', 'true');
    }

    if (!applySafeHighlight(range, mark)) {
      // console.warn(`Grifo ${h.id}: Falha ao aplicar highlight no nó único`);
      return false;
    }

    debugLog(`Grifo ${h.id}: Aplicado com sucesso usando nó único`);
    return true;
    
  } catch (error) {
    // console.warn(`Grifo ${h.id}: Erro ao aplicar highlight no nó único:`, error);
    return false;
  }
}

/**
 * Aplica um highlight complexo (múltiplos nós)
 */
export function applyComplexHighlight(
  h: Highlight,
  targetContainer: Element,
  onRemove?: (id: string) => void
): boolean {
  // Usa o sistema avançado de fallback para ambos os nós
  let startNode = getNodeFromPathWithAdvancedFallback(h, targetContainer);
  let endNode = startNode; // Para highlights complexos, pode ser o mesmo nó

  // Se não encontrou via sistema avançado, tenta o sistema original
  if (!startNode) {
    startNode = getNodeFromPathWithFallback(h.startContainerPath, targetContainer, h.text, true);
    endNode = getNodeFromPathWithFallback(h.endContainerPath, targetContainer, h.text, false);
  }

  if (!startNode || !endNode) {
    // console.warn(`Grifo ${h.id}: Nós não encontrados no DOM atual`, {
    //   startPath: h.startContainerPath,
    //   endPath: h.endContainerPath,
    //   startFound: !!startNode,
    //   endFound: !!endNode
    // });

    // Fallback: tenta usar o mesmo nó para ambos os pontos
    if (startNode && !endNode) {
      debugLog(`Grifo ${h.id}: Usando startNode para ambos os pontos`);
      return applySingleNodeHighlight(h, targetContainer, onRemove);
    } else if (!startNode && endNode) {
      debugLog(`Grifo ${h.id}: Usando endNode para ambos os pontos`);
      return applySingleNodeHighlight(h, targetContainer, onRemove);
    }
    
    return false;
  }

  // Validação de tipos de nó
  if (startNode.nodeType !== Node.TEXT_NODE || endNode.nodeType !== Node.TEXT_NODE) {
    // console.warn(`Grifo ${h.id}: Nós não são text nodes válidos`, {
    //   startNodeType: startNode.nodeType,
    //   endNodeType: endNode.nodeType
    // });
    return false;
  }

  return applyHighlightToRange(h, startNode, endNode, targetContainer, onRemove);
}

/**
 * Aplica highlight a um range específico
 */
function applyHighlightToRange(
  h: Highlight, 
  startNode: Node, 
  endNode: Node, 
  targetContainer: Element, 
  onRemove?: (id: string) => void
): boolean {
  const startText = startNode.textContent || '';
  const endText = endNode.textContent || '';
  const startLen = startText.length;
  const endLen = endText.length;

  // Validação de conteúdo dos nós
  if (startLen === 0 || endLen === 0) {
    // console.warn(`Grifo ${h.id}: Text nodes vazios`, {
    //   startLen,
    //   endLen
    // });
    return false;
  }

  // Calcula offsets seguros
  let safeStart = Math.max(0, Math.min(h.startOffset, startLen - 1));
  let safeEnd = Math.max(1, Math.min(h.endOffset, endLen));

  // Se os offsets originais são inválidos, tenta calcular baseado no texto
  if (safeStart >= startLen || safeEnd > endLen || safeStart >= safeEnd) {
    // console.warn(`Grifo ${h.id}: Offsets inválidos após ajustes`, {
    //   safeStart,
    //   safeEnd,
    //   startLen,
    //   endLen,
    //   originalStart: h.startOffset,
    //   originalEnd: h.endOffset
    // });

    // Fallback: busca o texto do highlight nos nós
    if (h.text && h.text.trim().length > 0) {
      const textIndex = startText.indexOf(h.text.trim());
      if (textIndex !== -1) {
        debugLog(`Grifo ${h.id}: Usando posição baseada no texto do highlight`);
        safeStart = textIndex;
        safeEnd = Math.min(textIndex + h.text.length, startNode === endNode ? startLen : endLen);
        
        if (safeStart < startLen && safeEnd <= endLen && safeStart < safeEnd) {
          debugLog(`Grifo ${h.id}: Offsets baseados em texto são válidos`);
        } else {
          return false;
        }
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  try {
    const range = document.createRange();
    range.setStart(startNode, safeStart);
    range.setEnd(endNode, safeEnd);
    
    const rangeText = range.toString();
    
    if (range.collapsed || rangeText.trim().length === 0) {
      // console.warn(`Grifo ${h.id}: Range vazio ou colapsado`, {
      //   collapsed: range.collapsed,
      //   rangeText: rangeText.substring(0, 50),
      //   safeStart,
      //   safeEnd
      // });
      
      // Tenta expandir o range se estiver colapsado
      if (range.collapsed && startNode === endNode && safeStart < startLen - 1) {
        debugLog(`Grifo ${h.id}: Tentando expandir range colapsado`);
        try {
          const expandedRange = document.createRange();
          expandedRange.setStart(startNode, safeStart);
          expandedRange.setEnd(startNode, Math.min(safeStart + 10, startLen));
          
          if (!expandedRange.collapsed && expandedRange.toString().trim().length > 0) {
            debugLog(`Grifo ${h.id}: Range expandido com sucesso`);
            // Continua com o range expandido
            range.detach();
            return applyMarkToRange(h, expandedRange, targetContainer, onRemove);
          } else {
            expandedRange.detach();
          }
        } catch (expandError) {
          // console.warn(`Grifo ${h.id}: Falha ao expandir range:`, expandError);
          return false;
        }
      }
      
      range.detach();
      return false;
    }

    return applyMarkToRange(h, range, targetContainer, onRemove);
    
  } catch (e) {
    // console.warn(`Grifo ${h.id}: Falha ao definir range:`, e);
    return false;
  }
}

/**
 * Aplica a marca de highlight ao range
 */
function applyMarkToRange(
  h: Highlight, 
  range: Range, 
  targetContainer: Element, 
  onRemove?: (id: string) => void
): boolean {
  // Verifica sobreposição
  const startNode = range.startContainer;
  const endNode = range.endContainer;
  
  if (hasOverlappingHighlight(startNode, endNode, range.startOffset, range.endOffset, targetContainer)) {
    // console.warn(`Grifo ${h.id} sobrepõe com grifo existente, pulando`);
    return true; // Retorna true para não tentar novamente
  }

  const mark = document.createElement('mark');
  mark.style.backgroundColor = h.color;
  mark.dataset.grifo = h.id;
  mark.className = 'highlight-mark';
  mark.style.cursor = 'pointer';
  mark.title = 'Clique para remover grifo';

  if (!applySafeHighlight(range, mark)) {
    // console.warn(`Falha ao aplicar grifo ${h.id} com método seguro`);
    range.detach();
    return false;
  }

  debugLog(`Grifo ${h.id} aplicado com sucesso`, {
    text: h.text?.substring(0, 30),
    color: h.color
  });

  // Adiciona evento de clique para remoção direta
  if (onRemove) {
    // Função de remoção
    const handleRemove = (e: Event) => {
      e.preventDefault();
      e.stopPropagation();
      e.stopImmediatePropagation();
      console.log('🗑️ Removendo grifo:', h.id); // Log sempre visível para debug
      onRemove(h.id);
    };

    // Múltiplos eventos para garantir que funcione
    mark.addEventListener('mousedown', (e) => {
      if (e.button === 0) { // Só clique esquerdo
        handleRemove(e);
      }
    }, { capture: true });

    mark.addEventListener('click', handleRemove, { capture: true });
    mark.addEventListener('touchstart', handleRemove, { capture: true });

    // Adiciona cursor pointer para indicar que é clicável
    mark.style.cursor = 'pointer';
    mark.title = 'Clique para remover o grifo';

    // Adiciona atributo para facilitar debug
    mark.setAttribute('data-removable', 'true');

    debugLog('Added removal events to highlight:', h.id);
  }

  return true;
}
