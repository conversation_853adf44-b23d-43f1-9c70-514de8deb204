import React, { useState, useEffect } from 'react';
import { ChevronDown, ChevronUp, Layers } from 'lucide-react';

interface CompactHierarchicalNavProps {
  context: string;
  debugInfo?: {
    totalHeadings: number;
    visibleHeadings: number;
    currentScroll: number;
    detectedElements: string[];
  };
  isVisible: boolean;
  onToggle: () => void;
}

export function CompactHierarchicalNav({ 
  context, 
  debugInfo, 
  isVisible, 
  onToggle 
}: CompactHierarchicalNavProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Detect mobile device
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Parse context into hierarchy levels and format compactly
  const formatCompactHierarchy = (context: string): string => {
    if (!context) return '';
    
    const levels = context.split(' • ').filter(Boolean);
    return levels.map(level => {
      const trimmed = level.trim();
      // Convert to compact format: "L. Livro I" -> "[L1. LIVRO I]"
      if (trimmed.startsWith('L.')) {
        const content = trimmed.substring(2).trim();
        const match = content.match(/^(.+?)(\s+[IVX\d]+)?(.*)$/);
        if (match) {
          const [, type, number = '', rest = ''] = match;
          return `[L${number.trim() || '1'}. ${type.toUpperCase()}${rest}]`;
        }
        return `[L1. ${content.toUpperCase()}]`;
      }
      if (trimmed.startsWith('T.')) {
        const content = trimmed.substring(2).trim();
        const match = content.match(/^(.+?)(\s+[IVX\d]+)?(.*)$/);
        if (match) {
          const [, type, number = '', rest = ''] = match;
          return `[T${number.trim() || '1'}. ${type.toUpperCase()}${rest}]`;
        }
        return `[T1. ${content.toUpperCase()}]`;
      }
      if (trimmed.startsWith('C.')) {
        const content = trimmed.substring(2).trim();
        const match = content.match(/^(.+?)(\s+[IVX\d]+)?(.*)$/);
        if (match) {
          const [, type, number = '', rest = ''] = match;
          return `[C${number.trim() || '1'}. ${type.toUpperCase()}${rest}]`;
        }
        return `[C1. ${content.toUpperCase()}]`;
      }
      if (trimmed.startsWith('S.')) {
        const content = trimmed.substring(2).trim();
        const match = content.match(/^(.+?)(\s+[IVX\d]+)?(.*)$/);
        if (match) {
          const [, type, number = '', rest = ''] = match;
          return `[S${number.trim() || '1'}. ${type.toUpperCase()}${rest}]`;
        }
        return `[S1. ${content.toUpperCase()}]`;
      }
      return `[${trimmed.toUpperCase()}]`;
    }).join('');
  };

  const compactHierarchy = formatCompactHierarchy(context);

  if (!isVisible) {
    return (
      <button
        onClick={onToggle}
        className="fixed top-4 right-4 z-40 bg-gray-800 bg-opacity-90 backdrop-blur-sm rounded-lg shadow-lg border border-gray-600 p-2 hover:bg-gray-700 transition-colors"
        title="Mostrar navegação hierárquica"
      >
        <Layers size={16} className="text-white" />
      </button>
    );
  }

  return (
    <div className="fixed top-4 right-4 z-40">
      {/* Collapsed state - compact hierarchy display */}
      {!isExpanded && (
        <div className="bg-gray-800 bg-opacity-95 backdrop-blur-sm rounded-lg shadow-xl border border-gray-600 max-w-md">
          <div className="flex items-center justify-between p-2">
            <div className="flex items-center gap-2 flex-1 min-w-0">
              <Layers size={14} className="text-gray-400 flex-shrink-0" />
              <div className="text-xs text-white font-mono truncate">
                {compactHierarchy || 'Carregando...'}
              </div>
            </div>
            <div className="flex items-center gap-1 ml-2">
              <button
                onClick={() => setIsExpanded(true)}
                className="text-gray-400 hover:text-white p-1 rounded hover:bg-gray-700"
                title="Expandir detalhes"
              >
                <ChevronDown size={12} />
              </button>
              <button
                onClick={onToggle}
                className="text-gray-400 hover:text-white p-1 rounded hover:bg-gray-700"
                title="Ocultar navegação"
              >
                ×
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Expanded state - detailed hierarchy */}
      {isExpanded && (
        <div className="bg-gray-800 bg-opacity-95 backdrop-blur-sm rounded-lg shadow-xl border border-gray-600 min-w-80 max-w-md">
          <div className="flex items-center justify-between p-3 border-b border-gray-600">
            <div className="flex items-center gap-2">
              <Layers size={16} className="text-gray-400" />
              <span className="text-sm text-gray-300 font-medium">
                Contexto Hierárquico
              </span>
            </div>
            <div className="flex items-center gap-1">
              <button
                onClick={() => setIsExpanded(false)}
                className="text-gray-400 hover:text-white p-1 rounded hover:bg-gray-700"
                title="Minimizar"
              >
                <ChevronUp size={14} />
              </button>
              <button
                onClick={onToggle}
                className="text-gray-400 hover:text-white p-1 rounded hover:bg-gray-700"
                title="Ocultar navegação"
              >
                ×
              </button>
            </div>
          </div>
          
          <div className="p-3">
            {context ? (
              <div className="space-y-2">
                {context.split(' • ').filter(Boolean).map((level, idx) => {
                  const levelLower = level.toLowerCase();
                  let bgColor = 'bg-gray-600';
                  
                  if (levelLower.startsWith('l.')) bgColor = 'bg-red-600';
                  else if (levelLower.startsWith('t.')) bgColor = 'bg-blue-600';
                  else if (levelLower.startsWith('c.')) bgColor = 'bg-green-600';
                  else if (levelLower.startsWith('s.')) bgColor = 'bg-amber-600';
                  else if (levelLower.startsWith('ss.')) bgColor = 'bg-purple-600';
                  
                  return (
                    <div key={idx} className="flex items-center gap-2">
                      <span className={`text-white font-medium rounded-md px-2 py-1 text-xs shadow-sm ${bgColor}`}>
                        {level.trim()}
                      </span>
                    </div>
                  );
                })}
              </div>
            ) : (
              <div className="text-gray-400 text-xs italic">
                Nenhum contexto hierárquico detectado
              </div>
            )}
            
            {/* Debug info in expanded mode */}
            {process.env.NODE_ENV === 'development' && debugInfo && (
              <div className="mt-3 pt-3 border-t border-gray-600">
                <div className="text-xs text-gray-400">
                  <div>Cabeçalhos: {debugInfo.totalHeadings} | Visíveis: {debugInfo.visibleHeadings}</div>
                  <div>Scroll: {Math.round(debugInfo.currentScroll)}px</div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
