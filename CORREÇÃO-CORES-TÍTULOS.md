# Correção das Cores dos Títulos Hierárquicos

## Problema Identificado

Os retângulos hier<PERSON> (títulos como LIVRO I, TÍTULO I, CAPÍTULO I, etc.) não estavam recebendo as cores corretas conforme definido no CSS. O problema estava na função `combineTitlesInDOM` em `src/utils/htmlUtils.ts`.

## Padrão HTML Identificado

Baseado na análise da imagem fornecida, o padrão HTML é:
```html
<p class="heading heading-livro">LIVRO I</p>
<p class="Cap">DAS DISPOSIÇÕES PRELIMINARES</p>
```

## Mudanças Implementadas

### 1. Melhorada a função `getTitleType` (src/utils/htmlUtils.ts)
- Adicionado `toUpperCase()` para garantir detecção correta
- Adicionado fallback `title-default` para casos não identificados

### 2. Adicionado CSS para classe padrão (src/styles/lei.css)
```css
#lei-container p.combined-title.title-default {
  border-left-color: #6b7280 !important;
  background: rgba(55, 65, 81, 0.85) !important;
}
```

### 3. Melhorada a lógica de detecção de títulos
- Adicionado padrão específico para `heading + Cap`
- Melhorada a detecção de títulos já processados
- Adicionado processamento inicial para títulos com classes existentes

### 4. Cores por Hierarquia
- **LIVRO**: Vermelho (`#ef4444` / `rgba(127, 29, 29, 0.85)`)
- **TÍTULO**: Azul (`#3b82f6` / `rgba(30, 58, 138, 0.85)`)
- **CAPÍTULO**: Verde (`#10b981` / `rgba(6, 78, 59, 0.85)`)
- **SEÇÃO**: Âmbar (`#f59e0b` / `rgba(146, 64, 14, 0.85)`)
- **PARTE**: Roxo (`#8b5cf6` / `rgba(91, 33, 182, 0.85)`)
- **DEFAULT**: Cinza (`#6b7280` / `rgba(55, 65, 81, 0.85)`)

## Como Testar

1. Abra a aplicação em `http://localhost:5177/`
2. Navegue para qualquer lei (ex: Código Civil)
3. Verifique se os títulos hierárquicos agora têm cores diferentes:
   - LIVRO I deve aparecer com fundo vermelho escuro
   - TÍTULO I deve aparecer com fundo azul escuro
   - CAPÍTULO I deve aparecer com fundo verde escuro
   - SEÇÃO I deve aparecer com fundo âmbar escuro

## Arquivo de Teste

Foi criado um arquivo `test-title-colors.html` para testar as cores isoladamente.

## Problema Adicional Identificado e Resolvido

### Textos das Leis Não Carregavam

**Causa:** O backend estava rodando mas havia um problema de CORS que impedia as requisições do frontend.

**Solução:**
1. **Backend não estava iniciado:** Iniciado o backend na porta 3005
2. **Problema de CORS:** Modificado `backend/index.js` para permitir requisições com `origin: null` (necessário para desenvolvimento)
3. **URL do backend incorreta:** Corrigido `.env` para usar `http://localhost:3005`

### Mudanças no Backend (backend/index.js)
```javascript
// Antes
callback(new Error('Not allowed by CORS'));

// Depois
callback(null, true); // Temporarily allow all origins for development
```

### Mudanças no .env
```
# Antes
VITE_BACKEND_URL=http://*************:3005

# Depois
VITE_BACKEND_URL=http://localhost:3005
```

## Status Final

✅ **Cores dos títulos hierárquicos:** FUNCIONANDO
✅ **Carregamento das leis:** FUNCIONANDO
✅ **Backend:** RODANDO na porta 3005
✅ **Frontend:** RODANDO na porta 5173

## Arquivos de Teste Criados

- `test-title-colors.html` - Teste das cores dos títulos
- `test-backend-connection.html` - Teste da conexão com backend
