import { useState, useCallback } from 'react';

export function useHighlightMenu() {
  const [showGrifoMenu, setShowGrifoMenu] = useState(false);
  const [grifoMenuPos, setGrifoMenuPos] = useState({ top: 0, left: 0 });
  const [selectedText, _setSelectedText] = useState("");
  const [selection, setSelection] = useState<Selection | null>(null);

  const setSelectedText = useCallback((text: string, sel?: Selection) => {
    if (sel && text.trim().length > 0) {
      const range = sel.getRangeAt(0);
      const rect = range.getBoundingClientRect();
      setGrifoMenuPos({
        top: rect.bottom + 8, // Position below the selection
        left: rect.left + rect.width / 2, // Center horizontally
      });
      _setSelectedText(text);
      setSelection(sel);
      setShowGrifoMenu(true);
    } else {
      _setSelectedText("");
      setSelection(null);
      setShowGrifoMenu(false);
    }
  }, []);

  return { showGrifoMenu, grifoMenuPos, selectedText, selection, setSelectedText, setShowGrifoMenu };
}
