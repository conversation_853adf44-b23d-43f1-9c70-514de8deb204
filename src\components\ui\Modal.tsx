import { useEffect } from 'react';
import ReactDOM from 'react-dom';

interface ModalProps {
  children: React.ReactNode;
  onClose: () => void;
  isOpen: boolean;
  size?: 'sm' | 'md' | 'lg' | 'xl';
}

const Modal = ({ children, onClose, isOpen, size = 'sm' }: ModalProps) => {
  useEffect(() => {
    const handleEsc = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };
    window.addEventListener('keydown', handleEsc);

    return () => {
      window.removeEventListener('keydown', handleEsc);
    };
  }, [onClose]);

  if (!isOpen) return null;

  const getSizeClasses = () => {
    switch (size) {
      case 'sm': return 'max-w-xs';
      case 'md': return 'max-w-md';
      case 'lg': return 'max-w-2xl';
      case 'xl': return 'max-w-4xl';
      default: return 'max-w-xs';
    }
  };

  return ReactDOM.createPortal(
    <div
      className="fixed inset-0 z-50 bg-neutral-900/70 backdrop-blur-sm animate-fadeIn p-4 flex items-center justify-center"
      onClick={onClose}
    >
      <div
        className={`modal-bg rounded-2xl shadow-lg w-full ${getSizeClasses()} p-4 animate-scaleIn max-h-[90vh] overflow-y-auto`}
        onClick={(e) => e.stopPropagation()}
      >
        {children}
      </div>
    </div>,
    document.body
  );
};

export default Modal;
