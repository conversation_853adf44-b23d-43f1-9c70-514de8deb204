import React from "react";
import type { Highlight } from "../hooks/useHighlights";
import { useHighlightRenderer } from "../hooks/useHighlightRenderer";

interface Props {
  containerId: string;
  highlights: Highlight[];
  getNodeFromPath: (path: number[], root: Node) => Node | null;
  onRemove?: (id: string) => void;
}

/**
 * Componente simplificado para reaplicar grifos em HTML já renderizado.
 * Agora usa o hook useHighlightRenderer para toda a lógica complexa.
 */
export const ReapplyHighlights: React.FC<Props> = ({ containerId, highlights, getNodeFromPath, onRemove }) => {
  useHighlightRenderer({
    containerId,
    highlights,
    getNodeFromPath,
    onRemove
  });

  return null;
};