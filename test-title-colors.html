<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste de Cores dos Títulos Hierárquicos</title>
    <style>
        /* Estilos dos títulos combinados copiados do lei.css */
        #lei-container p.combined-title {
            background: rgba(15, 23, 42, 0.92) !important;
            border: none !important;
            border-left: 3px solid #3b82f6 !important;
            padding: 0.7rem 1rem !important;
            margin: 1.5rem 0 1rem 0 !important;
            border-radius: 4px !important;
            font-weight: 600 !important;
            font-size: 0.95em !important;
            letter-spacing: 0.3px !important;
            text-transform: uppercase !important;
            color: #f1f5f9 !important;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12) !important;
            text-align: left !important;
            line-height: 1.3 !important;
        }

        /* Cores específicas por hierarquia */
        #lei-container p.combined-title.title-livro {
            border-left-color: #ef4444 !important;
            background: rgba(127, 29, 29, 0.85) !important;
        }

        #lei-container p.combined-title.title-titulo {
            border-left-color: #3b82f6 !important;
            background: rgba(30, 58, 138, 0.85) !important;
        }

        #lei-container p.combined-title.title-capitulo {
            border-left-color: #10b981 !important;
            background: rgba(6, 78, 59, 0.85) !important;
        }

        #lei-container p.combined-title.title-secao {
            border-left-color: #f59e0b !important;
            background: rgba(146, 64, 14, 0.85) !important;
        }

        #lei-container p.combined-title.title-parte {
            border-left-color: #8b5cf6 !important;
            background: rgba(91, 33, 182, 0.85) !important;
        }

        #lei-container p.combined-title.title-default {
            border-left-color: #6b7280 !important;
            background: rgba(55, 65, 81, 0.85) !important;
        }
    </style>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h2 {
            color: #333;
            border-bottom: 2px solid #007acc;
            padding-bottom: 10px;
        }
    </style>
</head>
<body>
    <h1>Teste de Cores dos Títulos Hierárquicos</h1>
    
    <div class="test-container">
        <h2>Antes do Processamento</h2>
        <div id="lei-container-before">
            <p class="heading heading-livro">LIVRO I</p>
            <p class="Cap">DAS DISPOSIÇÕES PRELIMINARES</p>
            
            <p class="heading heading-titulo">TÍTULO I</p>
            <p class="Cap">DOS DIREITOS FUNDAMENTAIS</p>
            
            <p class="heading heading-capitulo">CAPÍTULO I</p>
            <p class="Cap">DO DIREITO À VIDA E À SAÚDE</p>
            
            <p class="heading heading-secao">SEÇÃO I</p>
            <p class="Cap">DAS DISPOSIÇÕES GERAIS</p>
        </div>
    </div>

    <div class="test-container">
        <h2>Depois do Processamento</h2>
        <div id="lei-container">
            <p class="heading heading-livro"><a name="livro1"></a>LIVRO I</p>
            <p class="Cap">DAS DISPOSIÇÕES PRELIMINARES</p>

            <p class="heading heading-titulo"><a name="titulo1"></a>TÍTULO I</p>
            <p class="Cap">DOS DIREITOS FUNDAMENTAIS</p>

            <p class="heading heading-capitulo"><a name="cap1"></a>CAPÍTULO I</p>
            <p class="Cap">DO DIREITO À VIDA E À SAÚDE</p>

            <p class="heading heading-secao"><a name="secao1"></a>SEÇÃO I</p>
            <p class="Cap">DAS DISPOSIÇÕES GERAIS</p>

            <p class="heading"><a name="parte1"></a>PARTE GERAL</p>
        </div>
    </div>

    <button onclick="processarTitulos()">Processar Títulos</button>
    <button onclick="verificarClasses()">Verificar Classes</button>

    <div id="resultado" style="margin-top: 20px; padding: 10px; background: #f0f0f0; border-radius: 4px;"></div>

    <script>
        // Simulação da função combineTitlesInDOM para teste
        function combineTitlesInDOM(container) {
            const paragraphs = Array.from(container.querySelectorAll('p'));

            // Função helper para determinar o tipo de título
            const getTitleType = (text) => {
                const upperText = text.toUpperCase();
                if (upperText.includes('LIVRO')) return 'title-livro';
                if (upperText.includes('TÍTULO') || upperText.includes('TITULO')) return 'title-titulo';
                if (upperText.includes('CAPÍTULO') || upperText.includes('CAPITULO')) return 'title-capitulo';
                if (upperText.includes('SEÇÃO') || upperText.includes('SECAO')) return 'title-secao';
                if (upperText.includes('PARTE')) return 'title-parte';
                return 'title-default';
            };

            // Padrão para títulos
            const processedTitlePattern = /^(LIVRO|PARTE|TÍTULO|TITULO|CAPÍTULO|CAPITULO|SEÇÃO|SECAO)\s+/i;

            // Primeiro, processar títulos que já podem ter classes mas não têm cores específicas
            paragraphs.forEach(p => {
                const text = p.textContent?.trim() || '';
                if (processedTitlePattern.test(text) &&
                    !p.classList.contains('combined-title') &&
                    (p.classList.contains('heading') || p.classList.contains('Cap') || p.classList.contains('livro') ||
                     p.classList.contains('titulo') || p.classList.contains('capitulo') || p.classList.contains('secao'))) {
                    const titleType = getTitleType(text);
                    p.classList.add('combined-title', titleType);
                    p.innerHTML = text.toUpperCase();
                }
            });

            // Processar combinações de títulos
            const toRemove = [];
            for (let i = 0; i < paragraphs.length - 1; i++) {
                const current = paragraphs[i];
                const next = paragraphs[i + 1];

                if (current.classList.contains('combined-title') || toRemove.includes(current)) continue;

                const currentText = current.textContent?.trim() || '';
                const nextText = next.textContent?.trim() || '';

                // Verificar se é um título seguido de descrição
                if (processedTitlePattern.test(currentText) &&
                    nextText && nextText.length > 3 &&
                    next.classList.contains('Cap')) {

                    const combinedTitle = `${currentText.toUpperCase()} - ${nextText.toUpperCase()}`;
                    current.innerHTML = combinedTitle;
                    current.className = `combined-title ${getTitleType(currentText)}`;
                    toRemove.push(next);
                }
            }

            // Remover elementos marcados para remoção
            toRemove.forEach(element => element.remove());
        }

        window.processarTitulos = function() {
            const container = document.getElementById('lei-container');
            if (container) {
                combineTitlesInDOM(container);
                console.log('Títulos processados!');
                verificarClasses();
            }
        };

        window.verificarClasses = function() {
            const container = document.getElementById('lei-container');
            const resultado = document.getElementById('resultado');
            if (container && resultado) {
                const paragraphs = container.querySelectorAll('p');
                let html = '<h3>Classes aplicadas:</h3>';
                paragraphs.forEach((p, index) => {
                    const text = p.textContent?.trim() || '';
                    const classes = p.className;
                    if (text.match(/^(LIVRO|TÍTULO|CAPÍTULO|SEÇÃO|PARTE)/i)) {
                        html += `<p><strong>${text}</strong> → Classes: <code>${classes}</code></p>`;
                    }
                });
                resultado.innerHTML = html;
            }
        };
    </script>
</body>
</html>
