import { initializeApp } from "firebase/app";
import { getAnalytics } from "firebase/analytics";
import { getAuth, signInAnonymously } from "firebase/auth";
import { getFirestore } from "firebase/firestore";

// Configuração lida de variáveis de ambiente definidas em `.env`.
// Crie um arquivo `.env` na raíz (ou copie o `.env.example`) com os valores do Firebase:
// VITE_FIREBASE_API_KEY="..."
// VITE_FIREBASE_AUTH_DOMAIN="..."
// etc.
const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: import.meta.env.VITE_FIREBASE_APP_ID,
};

let auth: ReturnType<typeof getAuth> | undefined;
let db: ReturnType<typeof getFirestore> | undefined;
let analytics: ReturnType<typeof getAnalytics> | undefined;
try {
  // Checa se a apiKey parece válida (começa com "AIza")
  if (firebaseConfig.apiKey && String(firebaseConfig.apiKey).startsWith("AIza")) {
    const app = initializeApp(firebaseConfig);
    auth = getAuth(app);
    db = getFirestore(app);
    analytics = getAnalytics(app);
    
    // Login anônimo removido: operações de leitura offline/local não necessitam autenticação.
    // Caso algum recurso futuramente exija, deve ser acionado explicitamente após consentimento do usuário.
    console.info("Firebase inicializado com sucesso.");
  } else {
    console.warn("Firebase não configurado (API KEY ausente ou inválida). O app funcionará em modo offline.");
  }
} catch (err) {
  console.error("Falha ao inicializar Firebase:", err);
}

export { auth, db, analytics }; 
