<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste Direto - CSS das Cores</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        #lei-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        /* Estilos base para títulos combinados - COPIADO DO ARQUIVO ORIGINAL */
        #lei-container p.combined-title {
            background: rgba(15, 23, 42, 0.92) !important;
            border: none !important;
            border-left: 3px solid #3b82f6 !important;
            padding: 0.7rem 1rem !important;
            margin: 1.5rem 0 1rem 0 !important;
            border-radius: 4px !important;
            font-weight: 600 !important;
            font-size: 0.95em !important;
            letter-spacing: 0.3px !important;
            text-transform: uppercase !important;
            color: #f1f5f9 !important;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12) !important;
            text-align: left !important;
            line-height: 1.3 !important;
        }
        
        /* Cores específicas por hierarquia - COPIADO DO ARQUIVO ORIGINAL */
        #lei-container p.combined-title.title-livro {
            border-left-color: #ef4444 !important;
            background: rgba(127, 29, 29, 0.85) !important;
            border-color: #ef4444 !important;
        }
        
        #lei-container p.combined-title.title-titulo {
            border-left-color: #3b82f6 !important;
            background: rgba(30, 58, 138, 0.85) !important;
            border-color: #3b82f6 !important;
        }
        
        #lei-container p.combined-title.title-capitulo {
            border-left-color: #10b981 !important;
            background: rgba(6, 78, 59, 0.85) !important;
            border-color: #10b981 !important;
        }
        
        #lei-container p.combined-title.title-secao {
            border-left-color: #f59e0b !important;
            background: rgba(146, 64, 14, 0.85) !important;
            border-color: #f59e0b !important;
        }
        
        #lei-container p.combined-title.title-parte {
            border-left-color: #8b5cf6 !important;
            background: rgba(91, 33, 182, 0.85) !important;
            border-color: #8b5cf6 !important;
        }
        
        #lei-container p.combined-title.title-default {
            border-left-color: #6b7280 !important;
            background: rgba(55, 65, 81, 0.85) !important;
            border-color: #6b7280 !important;
        }
        
        button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a9e;
        }
        
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>Teste Direto - CSS das Cores</h1>
    
    <div class="info">
        Este teste aplica as classes diretamente no HTML para verificar se o problema está no CSS ou na aplicação das classes.
    </div>
    
    <div id="lei-container">
        <h2>Títulos com Classes Aplicadas Diretamente:</h2>
        
        <p class="combined-title title-livro">LIVRO PRIMEIRO - DAS PESSOAS</p>
        <p class="combined-title title-titulo">TÍTULO I - DAS PESSOAS NATURAIS</p>
        <p class="combined-title title-capitulo">CAPÍTULO I - DA PERSONALIDADE E DA CAPACIDADE</p>
        <p class="combined-title title-secao">SEÇÃO I - DA PERSONALIDADE</p>
        <p class="combined-title title-parte">PARTE GERAL</p>
        <p class="combined-title title-default">DISPOSIÇÕES GERAIS</p>
        
        <h2>Títulos Sem Classes (Para Comparação):</h2>
        
        <p>LIVRO PRIMEIRO - DAS PESSOAS (sem classes)</p>
        <p>TÍTULO I - DAS PESSOAS NATURAIS (sem classes)</p>
        <p>CAPÍTULO I - DA PERSONALIDADE E DA CAPACIDADE (sem classes)</p>
        
        <h2>Teste de Aplicação Dinâmica:</h2>
        
        <p id="test-livro">LIVRO SEGUNDO - DOS BENS</p>
        <p id="test-titulo">TÍTULO II - DOS DIREITOS DA PERSONALIDADE</p>
        <p id="test-capitulo">CAPÍTULO II - DOS DIREITOS E DEVERES</p>
        <p id="test-secao">SEÇÃO II - DOS DIREITOS FUNDAMENTAIS</p>
        
        <button onclick="applyClasses()">Aplicar Classes Dinamicamente</button>
        <button onclick="removeClasses()">Remover Classes</button>
        <button onclick="inspectClasses()">Inspecionar Classes</button>
    </div>

    <div id="result" class="info" style="display: none;"></div>

    <script>
        function applyClasses() {
            document.getElementById('test-livro').className = 'combined-title title-livro';
            document.getElementById('test-titulo').className = 'combined-title title-titulo';
            document.getElementById('test-capitulo').className = 'combined-title title-capitulo';
            document.getElementById('test-secao').className = 'combined-title title-secao';
            
            showResult('Classes aplicadas dinamicamente!');
        }
        
        function removeClasses() {
            document.getElementById('test-livro').className = '';
            document.getElementById('test-titulo').className = '';
            document.getElementById('test-capitulo').className = '';
            document.getElementById('test-secao').className = '';
            
            showResult('Classes removidas!');
        }
        
        function inspectClasses() {
            const elements = [
                { id: 'test-livro', name: 'LIVRO' },
                { id: 'test-titulo', name: 'TÍTULO' },
                { id: 'test-capitulo', name: 'CAPÍTULO' },
                { id: 'test-secao', name: 'SEÇÃO' }
            ];
            
            let report = 'INSPEÇÃO DAS CLASSES:\n\n';
            
            elements.forEach(({ id, name }) => {
                const el = document.getElementById(id);
                const computedStyle = window.getComputedStyle(el);
                
                report += `${name}:\n`;
                report += `  Classes: "${el.className}"\n`;
                report += `  Background: ${computedStyle.backgroundColor}\n`;
                report += `  Border-left: ${computedStyle.borderLeftColor} ${computedStyle.borderLeftWidth}\n`;
                report += `  Color: ${computedStyle.color}\n\n`;
            });
            
            showResult(report);
        }
        
        function showResult(message) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.style.display = 'block';
        }
    </script>
</body>
</html>
