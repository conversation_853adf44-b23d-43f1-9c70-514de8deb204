import React, { useState } from 'react';
import { VirtualLei } from '../VirtualLei';
import { ReapplyHighlights } from '../ReapplyHighlights';
import { getNodeFromPath } from '../../hooks/useTextSelection';
import { useLawContext } from '../../hooks/useLawContext';
import { TableOfContents } from './TableOfContents';
import { useHierarchicalNavToggle } from '../../hooks/useHierarchicalNavToggle';
import { HierarchicalNavigation } from './HierarchicalNavigation';
import Modal from '../ui/Modal';
import { ProfileScreen } from '../../pages/ProfileScreen';
import { LawHeader } from './LawHeader';
import { PaywallModal } from './PaywallModal';
import { ArticleModal } from './ArticleModal';
import type { LocalLawViewProps } from '../../types/lawView';

export function LocalLawView({
  containerRef,
  leiMeta,
  navigate,
  corAtiva,
  setCorAtiva,
  diminuir,
  aumentar,
  exportarGrifos,
  importarGrifos,
  removeHighlight,
  virtualRef,
  visibleArtigos,
  handleScroll,
  highlights,
  tocItems,
  showPaywall,
  handlePaywallDismiss,
  creditsLeft,
  dialogArt,
  closeDialog,
  loadingExp,
  handleExplain,
  handleExplainWithExample,
  loadingJur,
  handleJurisprudence,
  dialogExp,
  dialogJur,
  formatIaText,
}: LocalLawViewProps) {
  // Get hierarchical context for local laws
  const { context: hierarchicalContext, debugInfo } = useLawContext(containerRef || { current: null }, [visibleArtigos]);

  // Hierarchical navigation toggle state
  const { isVisible: isHierarchicalNavVisible, toggle: toggleHierarchicalNav } = useHierarchicalNavToggle();

  // Profile modal state
  const [showProfileModal, setShowProfileModal] = useState(false);

  return (
    <div ref={containerRef as any} id="lei-container" className="h-screen flex flex-col scroll-auto" onScroll={handleScroll}>
      <LawHeader
        leiMeta={leiMeta}
        navigate={navigate}
        corAtiva={corAtiva}
        setCorAtiva={setCorAtiva}
        diminuir={diminuir}
        aumentar={aumentar}
        exportarGrifos={exportarGrifos}
        importarGrifos={importarGrifos}
        toggleHierarchicalNav={toggleHierarchicalNav}
        onProfileClick={() => setShowProfileModal(true)}
        isHierarchicalNavVisible={isHierarchicalNavVisible}
        hasHierarchicalContext={!!hierarchicalContext}
      />

      <VirtualLei
        ref={virtualRef}
        artigos={visibleArtigos.map(a => a.texto)}
        removeHighlight={removeHighlight}
      />

      {/* ReapplyHighlights component for local laws - target the entire container */}
      {highlights.length > 0 && (
        <ReapplyHighlights
          highlights={highlights}
          containerId="lei-container"
          getNodeFromPath={getNodeFromPath}
          onRemove={(id) => removeHighlight(id)}
        />
      )}

      <PaywallModal
        isVisible={showPaywall}
        creditsLeft={creditsLeft}
        navigate={navigate}
        onDismiss={handlePaywallDismiss}
      />

      <ArticleModal
        dialogArt={dialogArt}
        onClose={closeDialog}
        loadingExp={loadingExp}
        loadingJur={loadingJur}
        onExplain={handleExplain}
        onExplainWithExample={handleExplainWithExample}
        onJurisprudence={handleJurisprudence}
        dialogExp={dialogExp}
        dialogJur={dialogJur}
        formatIaText={formatIaText}
      />

      <TableOfContents
        tocItems={tocItems}
        onSelect={(id) => {
          const index = visibleArtigos.findIndex(a => a.id === id);
          if (index !== -1) {
            virtualRef.current?.scrollTo(index);
          }
        }}
      />

      {/* Enhanced Hierarchical Navigation */}
      {hierarchicalContext && isHierarchicalNavVisible && (
        <HierarchicalNavigation
          context={hierarchicalContext}
          debugInfo={debugInfo}
          onHide={toggleHierarchicalNav}
        />
      )}

      {/* Profile Modal */}
      <Modal isOpen={showProfileModal} onClose={() => setShowProfileModal(false)} size="lg">
        <ProfileScreen minimal={true} onClose={() => setShowProfileModal(false)} />
      </Modal>
    </div>
  );
};
