// Teste para verificar se a combinação de títulos está funcionando

// Simular o HTML que vemos na imagem
const testHtml = `
<p align="center" class="Cap" style="line-height: 12.0pt; margin-right: 0cm; margin-top: 20px; margin-bottom: 20px">
  <a name="partegerallivroi"></a>
  LIVRO I
</p>
<p align="center" class="Cap" style="line-height: 12.0pt; margin-right: 0cm; margin-top: 20px; margin-bottom: 20px">
  <a name="partegerallivroidas"></a>
  DAS NORMAS PROCESSUAIS CIVIS
</p>
`;

// Simular o HTML após processamento inicial (como aparece na imagem)
const processedHtml = `
<p class="heading">LIVRO I</p>
<p class="heading">DAS NORMAS PROCESSUAIS CIVIS</p>
`;

// Simular o HTML real que vemos nos logs
const realHtml = `
<p class="heading heading-livro"><a name="partegerallivroi"> </a> LIVRO I</p>
<p align="center" class="Cap" style="line-height: 12.0pt; margin-right: 0cm; margin-top: 20px; margin-bottom: 20px">
DAS NORMAS PROCESSUAIS CIVIS
</p>
`;

console.log('🧪 Testando combinação de títulos...');
console.log('📄 HTML original:', testHtml);
console.log('📄 HTML processado:', processedHtml);

// Simular a função combineSeperatedTitles
function testCombineSeperatedTitles(html) {
  console.log('🔍 Tentando combinar títulos separados...');
  
  // Debug: Vamos ver uma amostra do HTML que contém LIVRO
  const livroMatch = html.match(/([\s\S]{0,300}LIVRO[\s\S]{0,300})/i);
  if (livroMatch) {
    console.log('📄 Amostra do HTML ao redor de LIVRO:', livroMatch[1]);
  }
  
  let result = html;
  
  // Padrões para diferentes casos
  const patterns = [
    // Padrão 1: Elementos já processados com class="heading"
    /<p\s+class="heading[^"]*"[^>]*>\s*((?:LIVRO|PARTE|T[ÍI]TULO|CAP[ÍI]TULO|SE[ÇC][ÃA]O)\s+[IVX\d]+)\s*<\/p>\s*<p\s+class="heading[^"]*"[^>]*>\s*([A-ZÁÉÍÓÚÂÊÎÔÛÀÈÌÒÙÃÕÇ][^<]{5,80})\s*<\/p>/gi,

    // Padrão 2: Elemento processado (heading) seguido de elemento não processado (Cap) - mais flexível
    /<p\s+class="heading[^"]*"[^>]*>[\s\S]*?((?:LIVRO|PARTE|T[ÍI]TULO|CAP[ÍI]TULO|SE[ÇC][ÃA]O)\s+[IVX\d]+)[\s\S]*?<\/p>\s*<p[^>]*class="Cap"[^>]*>\s*([A-ZÁÉÍÓÚÂÊÎÔÛÀÈÌÒÙÃÕÇ][^<]{5,80})\s*<\/p>/gi
  ];
  
  let combinationCount = 0;
  
  patterns.forEach((pattern, index) => {
    result = result.replace(pattern, (match, title, description) => {
      const cleanTitle = title.trim();
      const cleanDesc = description.trim();

      console.log(`🎯 Padrão ${index + 1} encontrou:`, { cleanTitle, cleanDesc });

      // Verificar se é realmente um título e descrição válidos
      const isTitlePattern = /^(LIVRO|PARTE|T[ÍI]TULO|CAP[ÍI]TULO|SE[ÇC][ÃA]O)\s+[IVX\d]+$/i.test(cleanTitle);
      const isDescPattern = /^[A-ZÁÉÍÓÚÂÊÎÔÛÀÈÌÒÙÃÕÇ]/.test(cleanDesc) &&
                           cleanDesc.length >= 5 &&
                           cleanDesc.length <= 80 &&
                           !/^(LIVRO|PARTE|T[ÍI]TULO|CAP[ÍI]TULO|SE[ÇC][ÃA]O|Art\b)/i.test(cleanDesc);

      console.log(`📊 Validação:`, { isTitlePattern, isDescPattern });

      if (isTitlePattern && isDescPattern) {
        combinationCount++;
        const combined = `<p class="heading">${cleanTitle} - ${cleanDesc}</p>`;
        console.log(`✅ Combinação ${combinationCount}:`, combined);
        return combined;
      }

      return match;
    });
  });
  
  console.log(`📈 Total de combinações realizadas: ${combinationCount}`);
  return result;
}

// Testar com o HTML processado
console.log('\n🧪 Testando HTML processado:');
const result1 = testCombineSeperatedTitles(processedHtml);
console.log('🎯 Resultado final:', result1);

// Testar com o HTML real dos logs
console.log('\n🧪 Testando HTML real dos logs:');
const result2 = testCombineSeperatedTitles(realHtml);
console.log('🎯 Resultado final:', result2);
