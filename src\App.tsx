import { Routes, Route, Navigate } from "react-router-dom";
import { HomeScreen } from "./pages/HomeScreen";
import { StudyScreen } from "./pages/StudyScreen";
import { AuthGate } from "./components/AuthGate";
import { ThemeProvider } from "./components/ThemeProvider";
import { ErrorBoundary } from "./components/ErrorBoundary";
import { Toaster } from "react-hot-toast";
import { LoginScreen } from "./pages/LoginScreen";
import { ProfileScreen } from "./pages/ProfileScreen";

function App() {
  return (
    <ErrorBoundary>
      <ThemeProvider>
        <AuthGate>
          <Routes>
            <Route path="/home" element={<HomeScreen />} />
            <Route path="/" element={<Navigate to="/login" replace />} />
            <Route path="/lei/:leiId" element={<StudyScreen />} />
            <Route path="/login" element={<LoginScreen />} />
            <Route path="/perfil" element={<ProfileScreen />} />
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
          <Toaster position="bottom-right" />
        </AuthGate>
      </ThemeProvider>
    </ErrorBoundary>
  );
}

export default App;
