<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste de Conexão com Backend</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a9e;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .loading {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
    </style>
</head>
<body>
    <h1>Teste de Conexão com Backend</h1>
    
    <div class="test-container">
        <h2>Testes de Conectividade</h2>
        <button onclick="testBackendRoot()">Testar Backend Root (/)</button>
        <button onclick="testBackendLaws()">Testar Endpoint Laws</button>
        <button onclick="testFetchLawFunction()">Testar fetchLawHtml</button>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        function showResult(message, type = 'success') {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.className = `result ${type}`;
            resultDiv.style.display = 'block';
        }

        async function testBackendRoot() {
            showResult('Testando conexão com backend...', 'loading');
            
            try {
                const response = await fetch('http://localhost:3005/');
                const text = await response.text();
                showResult(`✅ Backend Root OK!\nStatus: ${response.status}\nResponse: ${text}`, 'success');
            } catch (error) {
                showResult(`❌ Erro ao conectar com backend root:\n${error.message}`, 'error');
            }
        }

        async function testBackendLaws() {
            showResult('Testando endpoint /laws...', 'loading');
            
            try {
                const testUrl = 'https://www.planalto.gov.br/ccivil_03/leis/2002/l10406compilada.htm';
                const response = await fetch(`http://localhost:3005/laws?url=${encodeURIComponent(testUrl)}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                const htmlLength = data.html ? data.html.length : 0;
                
                showResult(`✅ Endpoint /laws OK!\nStatus: ${response.status}\nHTML Length: ${htmlLength} characters\nFirst 200 chars: ${data.html ? data.html.substring(0, 200) + '...' : 'No HTML'}`, 'success');
            } catch (error) {
                showResult(`❌ Erro no endpoint /laws:\n${error.message}`, 'error');
            }
        }

        async function testFetchLawFunction() {
            showResult('Testando função fetchLawHtml...', 'loading');
            
            try {
                // Simular a função fetchLawHtml
                const BACKEND_URL = 'http://localhost:3005';
                const leiId = 'codigo-civil';
                const urlOriginal = 'https://www.planalto.gov.br/ccivil_03/leis/2002/l10406compilada.htm';
                
                const backendUrl = `${BACKEND_URL}/laws?url=${encodeURIComponent(urlOriginal)}`;
                console.log('Fetching from:', backendUrl);
                
                const response = await fetch(backendUrl, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                
                if (!data.html || typeof data.html !== 'string' || data.html.trim().length === 0) {
                    throw new Error('Servidor retornou conteúdo inválido');
                }
                
                if (data.html.includes('indisponível offline') || data.html.length < 1000) {
                    throw new Error('Conteúdo não está disponível');
                }
                
                showResult(`✅ fetchLawHtml simulado OK!\nLei ID: ${leiId}\nHTML Length: ${data.html.length} characters\nContém "Código Civil": ${data.html.includes('Código Civil')}\nContém "LIVRO": ${data.html.includes('LIVRO')}`, 'success');
                
            } catch (error) {
                showResult(`❌ Erro na função fetchLawHtml:\n${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
