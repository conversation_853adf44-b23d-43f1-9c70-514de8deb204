/**
 * Polyfill para crypto.randomUUID() que funciona em todos os dispositivos,
 * incluindo mobile onde crypto.randomUUID pode não estar disponível
 */

export function generateUUID(): string {
  // Tenta usar crypto.randomUUID se disponível (navegadores modernos)
  if (typeof crypto !== 'undefined' && crypto.randomUUID) {
    try {
      return crypto.randomUUID();
    } catch (error) {
      console.warn('crypto.randomUUID failed, falling back to polyfill:', error);
    }
  }

  // Fallback para dispositivos que não suportam crypto.randomUUID
  return generateUUIDPolyfill();
}

function generateUUIDPolyfill(): string {
  // Usa crypto.getRandomValues se disponível
  if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
    const array = new Uint8Array(16);
    crypto.getRandomValues(array);
    
    // Converte para formato UUID v4
    array[6] = (array[6] & 0x0f) | 0x40; // versão 4
    array[8] = (array[8] & 0x3f) | 0x80; // variante RFC4122
    
    const hex = Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
    return `${hex.slice(0, 8)}-${hex.slice(8, 12)}-${hex.slice(12, 16)}-${hex.slice(16, 20)}-${hex.slice(20, 32)}`;
  }

  // Fallback final usando Math.random (menos seguro mas funcional)
  console.warn('Using Math.random fallback for UUID generation - not cryptographically secure');
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

/**
 * Gera um ID curto para uso em elementos DOM (8 caracteres)
 */
export function generateShortId(): string {
  const fullUUID = generateUUID();
  return fullUUID.replace(/-/g, '').substring(0, 8);
}
