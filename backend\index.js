const express = require('express');
const cors     = require('cors');
const cheerio  = require('cheerio');
const { fetchRawHtml } = require('./scraper');
const { readCache, getFromCache, setToCache, isCacheValid } = require('./cache');

const app  = express();
const port = process.env.PORT || 3005;

// Initialize cache on startup
readCache();

// Configure CORS with specific allowed origins
const allowedOrigins = [
  'http://localhost:5173',  // Vite dev server
  'http://localhost:3000',  // Alternative dev port
  'https://your-domain.com', // Replace with your production domain
  // Add other allowed origins as needed
];

const corsOptions = {
  origin: function (origin, callback) {
    // Allow requests with no origin (like mobile apps, curl requests, or file:// protocol)
    if (!origin) return callback(null, true);

    if (allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      console.warn(`CORS blocked request from origin: ${origin}`);
      callback(null, true); // Temporarily allow all origins for development
    }
  },
  credentials: true,
  optionsSuccessStatus: 200 // Some legacy browsers (IE11, various SmartTVs) choke on 204
};

app.use(cors(corsOptions));
app.use(express.json({ limit: '10mb' })); // Limit payload size

// Input validation helpers
function isValidUrl(string) {
  try {
    const url = new URL(string);
    // Only allow HTTP and HTTPS protocols
    return url.protocol === 'http:' || url.protocol === 'https:';
  } catch (_) {
    return false;
  }
}

function isValidLeiId(id) {
  // Allow alphanumeric characters, hyphens, and underscores
  return /^[a-zA-Z0-9_-]+$/.test(id) && id.length <= 100;
}

/**
 * GET /laws/:id
 * Devolve { text, html } da lei compilada direto do Planalto.
 */
app.get('/laws/:id', async (req, res) => {
  const { id } = req.params;

  // Validate input
  if (!id || !isValidLeiId(id)) {
    return res.status(400).json({ error: 'Invalid law ID format' });
  }

  const planaltoUrl =
    `https://www.planalto.gov.br/ccivil_03/Leis/2002/L${id}compilada.htm`;

  try {
    const raw   = await fetchRawHtml(planaltoUrl);
    const $     = cheerio.load(raw);
    const block = $('.texto');
    res.json({ text: block.text().trim(), html: block.html() || '' });
  } catch (err) {
    console.error(`Error fetching law ${id}:`, err.message);
    res.status(err.response?.status || 500).json({ error: 'Failed to fetch law content' });
  }
});

/**
 * Health-check
 */
app.get('/test', (_, res) => res.send('OK'));

/**
 * GET /laws?url=ENCODED_URL
 * Returns { text, html } from the provided URL.
 */
app.get('/laws', async (req, res) => {
  const { url } = req.query;

  // Validate input
  if (!url) {
    return res.status(400).json({ error: 'Missing url query param' });
  }

  if (!isValidUrl(url)) {
    return res.status(400).json({ error: 'Invalid URL format' });
  }

  // Only allow planalto.gov.br URLs for security
  const urlObj = new URL(url);
  if (!urlObj.hostname.endsWith('planalto.gov.br')) {
    return res.status(403).json({ error: 'Only planalto.gov.br URLs are allowed' });
  }

  try {
    const raw = await fetchRawHtml(url);
    const $   = cheerio.load(raw);
    let block = $('.texto');
    if (!block.length || !block.html()) {
      block = $('body');
    }
    res.json({ text: block.text().trim(), html: block.html() || '' });
  } catch (err) {
    console.error(`Error fetching URL ${url}:`, err.message);
    res.status(err.response?.status || 500)
       .json({ error: 'Failed to fetch content from URL' });
  }
});

/**
 * Página HTML minimalista só com o texto da lei
 */
app.get('/view/laws/:id', async (req, res) => {
  const { id } = req.params;
  const planaltoUrl =
    `https://www.planalto.gov.br/ccivil_03/Leis/2002/L${id}compilada.htm`;

  try {
    const raw        = await fetchRawHtml(planaltoUrl);
    const $          = cheerio.load(raw);
    const contentHtml = $('.texto').html() || '';

    res.send(`<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="utf-8">
  <title>Lei ${id}</title>
  <style>body{font-family:Arial, sans-serif;margin:20px}</style>
</head>
<body>
${contentHtml}
</body>
</html>`);
  } catch (err) {
    res.status(err.response?.status || 500)
       .send(`Erro ${err.response?.status || 500}: ${err.message}`);
  }
});

app.listen(port, () =>
  console.log(`Backend server running at http://localhost:${port}`)
);