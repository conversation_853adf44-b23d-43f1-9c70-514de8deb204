export interface LeiMeta {
  id: string;
  nome: string;
  descricao: string;
  categoria: string;
  url?: string; // URL oficial do Planalto
  texto?: string; // Conteúdo local opcional
}

export const LEIS: LeiMeta[] = [
  {
    id: "codigo-civil",
    nome: "Código Civil",
    descricao: "Lei n.º 10.406/2002",
    categoria: "Códigos Principais",
    url: "https://www.planalto.gov.br/ccivil_03/leis/2002/l10406compilada.htm",
  },
  {
    id: "cdc",
    nome: "Código de Defesa do Consumidor",
    descricao: "Lei n.º 8.078/1990",
    categoria: "Códigos Principais",
    url: "https://www.planalto.gov.br/ccivil_03/leis/l8078compilado.htm",
  },
  {
    id: "codigo-penal",
    nome: "<PERSON>ódigo Penal",
    descricao: "Decreto-Lei n.º 2.848/1940",
    categoria: "Códigos Principais",
    url: "https://www.planalto.gov.br/ccivil_03/decreto-lei/del2848compilado.htm",
  },
  {
    id: "cpp",
    nome: "Código de Processo Penal",
    descricao: "Decreto-Lei n.º 3.689/1941",
    categoria: "Códigos de Processo",
    url: "https://www.planalto.gov.br/ccivil_03/decreto-lei/del3689compilado.htm",
  },
  {
    id: "clt",
    nome: "Consolidação das Leis do Trabalho (CLT)",
    descricao: "Decreto-Lei n.º 5.452/1943",
    categoria: "Códigos Principais",
    url: "https://www.planalto.gov.br/ccivil_03/decreto-lei/del5452compilado.htm",
  },
  {
    id: "constituicao-federal",
    nome: "Constituição Federal",
    descricao: "Constituição da República Federativa do Brasil de 1988",
    categoria: "Constituição",
    url: "https://www.planalto.gov.br/ccivil_03/constituicao/constituicao.htm",
  },
  {
    id: "cpc",
    nome: "Código de Processo Civil",
    descricao: "Lei n.º 13.105/2015",
    categoria: "Códigos de Processo",
    url: "https://www.planalto.gov.br/ccivil_03/_ato2015-2018/2015/lei/l13105.htm",
  },
  {
    id: "ctn",
    nome: "Código Tributário Nacional",
    descricao: "Lei n.º 5.172/1966",
    categoria: "Códigos Principais",
    url: "https://www.planalto.gov.br/ccivil_03/leis/l5172.htm",
  },
  {
    id: "eca",
    nome: "Estatuto da Criança e do Adolescente (ECA)",
    descricao: "Lei n.º 8.069/1990",
    categoria: "Estatutos",
    url: "https://www.planalto.gov.br/ccivil_03/leis/l8069.htm",
  },
  {
    id: "estatuto-idoso",
    nome: "Estatuto do Idoso",
    descricao: "Lei n.º 10.741/2003",
    categoria: "Estatutos",
    url: "https://www.planalto.gov.br/ccivil_03/leis/2003/l10.741.htm",
  },
];

// Permite que o usuário adicione códigos próprios que ficam salvos em localStorage
export function getCustomLeis(): LeiMeta[] {
  if (typeof localStorage === "undefined") return [];
  try {
    const raw = localStorage.getItem("custom_leis");
    return raw ? (JSON.parse(raw) as LeiMeta[]) : [];
  } catch {
    return [];
  }
}

export function getAllLeis(): LeiMeta[] {
  return [...LEIS, ...getCustomLeis()];
}

// Função auxiliar para salvar nova lei criada pelo usuário
export function saveCustomLei(lei: LeiMeta) {
  if (typeof localStorage === "undefined") return;
  const atuais = getCustomLeis();
  const semDuplicados = atuais.filter((l) => l.id !== lei.id);
  localStorage.setItem("custom_leis", JSON.stringify([...semDuplicados, lei]));
}

// Função auxiliar para remover lei personalizada
export function deleteCustomLei(leiId: string) {
  if (typeof localStorage === "undefined") return;
  const atuais = getCustomLeis();
  const filtradas = atuais.filter((l) => l.id !== leiId);
  localStorage.setItem("custom_leis", JSON.stringify(filtradas));
} 