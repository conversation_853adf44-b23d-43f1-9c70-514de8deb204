import { useState, useEffect } from "react";
import { ThemeToggle } from "../components/ui/ThemeToggle";
import { SearchBar } from "../components/ui/SearchBar";
import { LawCard } from "../components/LawCard";
import { getAllLeis, LeiMeta, deleteCustomLei, LEIS } from "../data/leis";
import React from "react";
import { auth } from "../firebase";
import { useNavigate } from "react-router-dom";
import { ProfileScreen } from "./ProfileScreen";
import { LoginScreen } from "./LoginScreen";
import Modal from "../components/ui/Modal";
import { NewLawForm } from "../components/NewLawForm";
import { SystematicIndex } from "../components/SystematicIndex";
import { useSystematicIndex } from "../hooks/useSystematicIndex";
import { useRemoteLaw } from "../hooks/useRemoteLaw";

// --- Utility Functions ---
function makeSlug(str: string): string {
  return str.normalize("NFD").replace(/[\u0300-\u036f]/g, "").toLowerCase().replace(/[^a-z0-9]+/g, "-").replace(/^[-]+|[-]+$/g, "");
}

// --- Main HomeScreen Component ---
export function HomeScreen() {
  const [query, setQuery] = useState("");
  const [leis, setLeis] = useState<LeiMeta[]>(getAllLeis);
  const [draggingId, setDraggingId] = useState<string | null>(null);
  const [dragOverId, setDragOverId] = useState<string | null>(null);
  const dragIdRef = React.useRef<string | null>(null);

  // --- Modal States ---
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [showProfileModal, setShowProfileModal] = useState(false);
  const [showNewLawModal, setShowNewLawModal] = useState(false);
  const [showSystematicIndex, setShowSystematicIndex] = useState(false);
  const [selectedLawForIndex, setSelectedLawForIndex] = useState<LeiMeta | null>(null);

  const handleLawAdded = (newLaw: LeiMeta) => {
    setLeis(currentLeis => {
      if (currentLeis.some(lei => lei.id === newLaw.id)) {
        return currentLeis; // Avoid duplicates
      }
      return [...currentLeis, newLaw];
    });
    setShowNewLawModal(false); // Close modal on success
  };
  const [showForm, setShowForm] = useState(false);
  const [actionsModal, setActionsModal] = useState<{ open: boolean; lei: LeiMeta | null }>({ open: false, lei: null });

  // --- Systematic Index Logic ---
  const { htmlRemoto, carregando: lawLoading } = useRemoteLaw(selectedLawForIndex?.id, selectedLawForIndex);
  const { items: systematicItems } = useSystematicIndex(htmlRemoto);

  const navigate = useNavigate();
  const user = auth?.currentUser;
  const guestMode = typeof localStorage !== 'undefined' && localStorage.getItem('guest_mode') === '1';

  // --- Drag and Drop Handlers ---
  const handleDragStart = (id: string) => { dragIdRef.current = id; setDraggingId(id); };
  const handleDragEnd = () => { setDraggingId(null); dragIdRef.current = null; setDragOverId(null); };
  const handleDrop = (targetId: string) => {
    const sourceId = dragIdRef.current;
    if (!sourceId || sourceId === targetId) return;
    setLeis(prev => {
      const srcIdx = prev.findIndex(l => l.id === sourceId);
      const tgtIdx = prev.findIndex(l => l.id === targetId);
      if (srcIdx === -1 || tgtIdx === -1) return prev;
      const newArr = [...prev];
      const [moved] = newArr.splice(srcIdx, 1);
      newArr.splice(tgtIdx, 0, moved);
      return newArr;
    });
    setDragOverId(null);
  };

  // --- Data Filtering ---
  const leisFiltradas = leis.filter(l => l.nome.toLowerCase().includes(query.toLowerCase()));
  const customLeis = leisFiltradas.filter(l => !LEIS.some(b => b.id === l.id));
  const baseSlice = leisFiltradas.filter(l => LEIS.some(b => b.id === l.id)).slice(0, 6);
  const leisParaExibir = guestMode ? [...customLeis, ...baseSlice] : leisFiltradas;

  // --- Action Handlers ---
  const handleOpenActionsModal = (lei: LeiMeta) => setActionsModal({ open: true, lei });
  const handleCloseActionsModal = () => setActionsModal({ open: false, lei: null });
  const handleDeleteLei = () => {
    if (!actionsModal.lei) return;
    if (confirm(`Excluir "${actionsModal.lei.nome}"? Essa ação é irreversível.`)) {
      deleteCustomLei(actionsModal.lei.id);
      setLeis(curr => curr.filter(l => l.id !== actionsModal.lei!.id));
      handleCloseActionsModal();
    }
  };

  const handleOpenSystematicIndex = (lei: LeiMeta) => {
    setSelectedLawForIndex(lei);
    setShowSystematicIndex(true);
  };

  const handleCloseSystematicIndex = () => {
    setShowSystematicIndex(false);
    setSelectedLawForIndex(null);
  };

  // Função de teste para demonstrar o índice sistemático
  const handleTestSystematicIndex = () => {
    const testLaw: LeiMeta = {
      id: 'test-cdc',
      nome: 'Código de Defesa do Consumidor (Teste)',
      descricao: 'Demonstração do índice sistemático',
      categoria: 'Teste'
    };
    setSelectedLawForIndex(testLaw);
    setShowSystematicIndex(true);
  };

  // --- Render ---
  return (
    <main className="p-6 max-w-4xl mx-auto">
      {/* Header */}
      <header className="flex items-center justify-between mb-8 gap-3 flex-wrap">
        <h1 className="text-3xl font-bold bg-gradient-to-r from-primary to-fuchsia-500 bg-clip-text text-transparent">Grifos Legais IA</h1>
        <div className="flex items-center gap-2">
          <button onClick={() => setShowProfileModal(true)} className="px-3 py-1 rounded-md bg-primary/20 hover:bg-primary/30 text-sm">Perfil</button>
          <button onClick={() => setShowNewLawModal(true)} className="px-3 py-1 rounded-md bg-primary/20 hover:bg-primary/30 text-sm">Adicionar Lei</button>
          <button onClick={handleTestSystematicIndex} className="px-3 py-1 rounded-md bg-blue-500/20 hover:bg-blue-500/30 text-sm">Teste Índice</button>
          {!user && <button onClick={() => guestMode ? setShowLoginModal(true) : navigate('/login')} className="px-3 py-1 rounded-md bg-primary/20 hover:bg-primary/30 text-sm">Entrar</button>}
          <ThemeToggle />
        </div>
      </header>

      <SearchBar query={query} setQuery={setQuery} />

      {/* Law Cards Grid */}
      <section className="mt-8 grid sm:grid-cols-2 lg:grid-cols-3 gap-4">
        {leisParaExibir.map(lei => (
          <div
            key={lei.id}
            draggable
            onDragStart={() => handleDragStart(lei.id)}
            onDragOver={e => { e.preventDefault(); setDragOverId(lei.id); }}
            onDrop={() => handleDrop(lei.id)}
            onDragEnd={handleDragEnd}
            onDragEnter={() => setDragOverId(lei.id)}
            onDragLeave={e => { if (!e.currentTarget.contains(e.relatedTarget as Node)) setDragOverId(null); }}
            className={`cursor-move duration-150 ease-in-out select-none ${
              draggingId === lei.id ? 'ring-2 ring-primary/50 opacity-90' : dragOverId === lei.id ? 'ring-2 ring-dashed ring-primary/40' : ''
            }`}
          >
            <LawCard
              {...lei}
              isDragging={draggingId === lei.id}
              onMenuClick={() => handleOpenActionsModal(lei)}
              onIndexClick={() => handleOpenSystematicIndex(lei)}
              isCustom={!LEIS.some(l => l.id === lei.id)}
            />
          </div>
        ))}
      </section>

      {/* Actions Modal (using the new Modal component) */}
      <Modal isOpen={actionsModal.open} onClose={handleCloseActionsModal}>
        {actionsModal.lei && (
          <div>
            <h3 className="text-lg font-semibold mb-4">Ações para: {actionsModal.lei.nome}</h3>
            <button
              onClick={handleDeleteLei}
              className="w-full text-left flex items-center gap-2 whitespace-nowrap rounded px-2 py-2 text-sm hover:bg-red-500 hover:text-white transition-colors"
            >
              Excluir
            </button>
            <button
              onClick={handleCloseActionsModal}
              className="w-full text-left mt-2 rounded px-2 py-2 text-sm hover:bg-neutral-200 dark:hover:bg-neutral-700 transition-colors"
            >
              Cancelar
            </button>
          </div>
        )}
      </Modal>

            {/* New Law Modal */}
      <Modal isOpen={showNewLawModal} onClose={() => setShowNewLawModal(false)}>
        <NewLawForm onClose={() => setShowNewLawModal(false)} onLawAdded={handleLawAdded} />
      </Modal>

      {/* Profile Modal */}
      <Modal isOpen={showProfileModal} onClose={() => setShowProfileModal(false)} size="lg">
        <ProfileScreen minimal={true} onClose={() => setShowProfileModal(false)} />
      </Modal>

      {/* Login Modal */}
      <Modal isOpen={showLoginModal} onClose={() => setShowLoginModal(false)}>
        <LoginScreen />
      </Modal>

      {/* Systematic Index Modal */}
      <SystematicIndex
        isOpen={showSystematicIndex}
        onClose={handleCloseSystematicIndex}
        lawName={selectedLawForIndex?.nome || ''}
        items={systematicItems}
        isLoading={lawLoading}
        onItemClick={(item) => {
          // Fechar o modal e navegar para a lei
          handleCloseSystematicIndex();
          if (selectedLawForIndex) {
            navigate(`/lei/${selectedLawForIndex.id}#${item.id}`);
          }
        }}
      />

    </main>
  );
}
