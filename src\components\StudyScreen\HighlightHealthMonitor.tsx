import React, { useState, useEffect } from 'react';
import { AlertTriangle, CheckCircle, RefreshCw, Trash2, Info } from 'lucide-react';

interface HealthReport {
  total: number;
  valid: number;
  invalid: number;
  withoutVersion: number;
  oldValidation: number;
}

interface Props {
  container: Element | null;
  getHealthReport: (container: Element) => HealthReport;
  validateIntegrity: (container: Element) => { valid: number; invalid: number; total: number };
  cleanupOrphaned: () => void;
  migrateHighlights: (container: Element, lawContent: string, lawVersion: string) => Promise<void>;
  lawContent?: string;
  lawVersion?: string;
}

export function HighlightHealthMonitor({
  container,
  getHealthReport,
  validateIntegrity,
  cleanupOrphaned,
  migrateHighlights,
  lawContent,
  lawVersion
}: Props) {
  const [healthReport, setHealthReport] = useState<HealthReport | null>(null);
  const [isExpanded, setIsExpanded] = useState(false);
  const [isValidating, setIsValidating] = useState(false);
  const [isCleaning, setIsCleaning] = useState(false);
  const [isMigrating, setIsMigrating] = useState(false);

  // Atualiza relatório quando container muda
  useEffect(() => {
    if (container) {
      const report = getHealthReport(container);
      setHealthReport(report);
    }
  }, [container, getHealthReport]);

  const handleValidateIntegrity = async () => {
    if (!container) return;
    
    setIsValidating(true);
    try {
      const result = validateIntegrity(container);
      console.info('[HighlightHealthMonitor] Validação de integridade:', result);
      
      // Atualiza o relatório
      const report = getHealthReport(container);
      setHealthReport(report);
    } catch (error) {
      console.error('[HighlightHealthMonitor] Erro na validação:', error);
    } finally {
      setIsValidating(false);
    }
  };

  const handleCleanupOrphaned = async () => {
    setIsCleaning(true);
    try {
      cleanupOrphaned();
      
      // Atualiza o relatório após limpeza
      if (container) {
        const report = getHealthReport(container);
        setHealthReport(report);
      }
    } catch (error) {
      console.error('[HighlightHealthMonitor] Erro na limpeza:', error);
    } finally {
      setIsCleaning(false);
    }
  };

  const handleMigrateHighlights = async () => {
    if (!container || !lawContent || !lawVersion) return;
    
    setIsMigrating(true);
    try {
      await migrateHighlights(container, lawContent, lawVersion);
      
      // Atualiza o relatório após migração
      const report = getHealthReport(container);
      setHealthReport(report);
    } catch (error) {
      console.error('[HighlightHealthMonitor] Erro na migração:', error);
    } finally {
      setIsMigrating(false);
    }
  };

  if (!healthReport || healthReport.total === 0) {
    return null;
  }

  const hasIssues = healthReport.invalid > 0 || healthReport.withoutVersion > 0 || healthReport.oldValidation > 0;
  const healthPercentage = Math.round((healthReport.valid / healthReport.total) * 100);

  // Só mostra se houver problemas ou em modo de desenvolvimento
  if (!hasIssues && !import.meta.env.DEV) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-lg border transition-all duration-300 ${
        isExpanded ? 'w-80' : 'w-auto'
      }`}>
        {/* Header compacto */}
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className={`flex items-center gap-2 p-3 w-full text-left hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors ${
            hasIssues ? 'text-amber-600 dark:text-amber-400' : 'text-green-600 dark:text-green-400'
          }`}
        >
          {hasIssues ? (
            <AlertTriangle size={16} />
          ) : (
            <CheckCircle size={16} />
          )}
          <span className="text-sm font-medium">
            Grifos: {healthPercentage}% ({healthReport.valid}/{healthReport.total})
          </span>
          {!isExpanded && hasIssues && (
            <span className="text-xs bg-amber-100 dark:bg-amber-900 text-amber-800 dark:text-amber-200 px-2 py-1 rounded">
              {healthReport.invalid + healthReport.withoutVersion + healthReport.oldValidation} problemas
            </span>
          )}
        </button>

        {/* Detalhes expandidos */}
        {isExpanded && (
          <div className="p-3 pt-0 border-t border-gray-200 dark:border-gray-600">
            <div className="space-y-3">
              {/* Estatísticas detalhadas */}
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Válidos:</span>
                  <span className="text-green-600 dark:text-green-400 font-medium">
                    {healthReport.valid}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Inválidos:</span>
                  <span className="text-red-600 dark:text-red-400 font-medium">
                    {healthReport.invalid}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Sem versão:</span>
                  <span className="text-amber-600 dark:text-amber-400 font-medium">
                    {healthReport.withoutVersion}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Desatualizados:</span>
                  <span className="text-amber-600 dark:text-amber-400 font-medium">
                    {healthReport.oldValidation}
                  </span>
                </div>
              </div>

              {/* Ações de manutenção */}
              <div className="space-y-2">
                <button
                  onClick={handleValidateIntegrity}
                  disabled={isValidating}
                  className="w-full flex items-center justify-center gap-2 px-3 py-2 text-xs bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 rounded hover:bg-blue-100 dark:hover:bg-blue-900/30 disabled:opacity-50 transition-colors"
                >
                  <RefreshCw size={12} className={isValidating ? 'animate-spin' : ''} />
                  {isValidating ? 'Validando...' : 'Validar Integridade'}
                </button>

                {healthReport.oldValidation > 0 && (
                  <button
                    onClick={handleCleanupOrphaned}
                    disabled={isCleaning}
                    className="w-full flex items-center justify-center gap-2 px-3 py-2 text-xs bg-amber-50 dark:bg-amber-900/20 text-amber-600 dark:text-amber-400 rounded hover:bg-amber-100 dark:hover:bg-amber-900/30 disabled:opacity-50 transition-colors"
                  >
                    <Trash2 size={12} />
                    {isCleaning ? 'Limpando...' : 'Limpar Órfãos'}
                  </button>
                )}

                {(healthReport.invalid > 0 || healthReport.withoutVersion > 0) && lawContent && lawVersion && (
                  <button
                    onClick={handleMigrateHighlights}
                    disabled={isMigrating}
                    className="w-full flex items-center justify-center gap-2 px-3 py-2 text-xs bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400 rounded hover:bg-green-100 dark:hover:bg-green-900/30 disabled:opacity-50 transition-colors"
                  >
                    <RefreshCw size={12} className={isMigrating ? 'animate-spin' : ''} />
                    {isMigrating ? 'Migrando...' : 'Migrar Grifos'}
                  </button>
                )}
              </div>

              {/* Informações adicionais */}
              {hasIssues && (
                <div className="flex items-start gap-2 p-2 bg-amber-50 dark:bg-amber-900/20 rounded text-xs">
                  <Info size={12} className="text-amber-600 dark:text-amber-400 mt-0.5 flex-shrink-0" />
                  <div className="text-amber-700 dark:text-amber-300">
                    <p className="font-medium mb-1">Problemas detectados:</p>
                    <ul className="space-y-1 text-xs">
                      {healthReport.invalid > 0 && (
                        <li>• {healthReport.invalid} grifos não encontrados no texto atual</li>
                      )}
                      {healthReport.withoutVersion > 0 && (
                        <li>• {healthReport.withoutVersion} grifos sem informação de versão</li>
                      )}
                      {healthReport.oldValidation > 0 && (
                        <li>• {healthReport.oldValidation} grifos não validados recentemente</li>
                      )}
                    </ul>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
