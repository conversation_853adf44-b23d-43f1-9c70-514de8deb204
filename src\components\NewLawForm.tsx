import React, { useState } from 'react';
import { LeiMeta, saveCustomLei, getAllLeis } from '../data/leis';
import { db } from '../firebase';
import { doc, getDoc, setDoc } from 'firebase/firestore';

interface NewLawFormProps {
  onClose: () => void;
  onLawAdded: (newLaw: LeiMeta) => void;
}

export const NewLawForm: React.FC<NewLawFormProps> = ({ onClose, onLawAdded }) => {
  const [url, setUrl] = useState('');
  const [customName, setCustomName] = useState('');
  const [customDescription, setCustomDescription] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);



  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!url.includes('planalto.gov.br')) {
        setError('Por favor, insira um link válido do site do Planalto.');
        return;
    }
    setLoading(true);
    setError(null);

    try {
      // 1. Extrair ID da lei da URL
      const lawDetails = extractLawIdFromUrl(url);
      if (!lawDetails) {
        throw new Error('URL inválida. Use o formato: http://www.planalto.gov.br/ccivil_03/leis/l8213.htm');
      }

      // 2. Verificar nas leis existentes (localStorage + base)
      const existingLaws = getAllLeis();
      const existingLaw = existingLaws.find((lei) => lei.id === lawDetails.id);
      if (existingLaw) {
        alert('Esta lei já existe no armazenamento local.');
        setLoading(false);
        onClose();
        return;
      }

      // 3. Firestore law lookup removed - laws are no longer stored in Firebase
      // Skip directly to creating the custom law entry

      // 4. Create custom law entry (API integration can be added later)
      // Simulação de chamada de API
      console.log(`Buscando lei com ID: ${lawDetails.id}`);
      // Por enquanto, vamos usar um nome e descrição placeholder.
      // A busca real do conteúdo da lei será implementada depois.
      await new Promise(resolve => setTimeout(resolve, 500)); // Simula delay da API

      const newLaw: LeiMeta = {
        id: lawDetails.id,
        nome: customName || `${lawDetails.type} nº ${lawDetails.number}`,
        descricao: customDescription || `${lawDetails.type} nº ${lawDetails.number} adicionada pelo usuário.`,
        categoria: 'Personalizada',
        url: url,
      };

      // 5. Salvar localmente e no Firestore
      saveCustomLei(newLaw);

      if (db) {
        try {
          const lawRef = doc(db, 'leis', newLaw.id);
          await setDoc(lawRef, newLaw);
          console.log('Lei salva no Firestore com sucesso!');
        } catch (dbError) {
          console.error('Erro ao salvar no Firestore:', dbError);
          // Opcional: informar o usuário que o salvamento na nuvem falhou
        }
      }

      alert('Lei adicionada com sucesso!');
      onLawAdded(newLaw);

    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const extractLawIdFromUrl = (url: string): { id: string; type: string; number: string } | null => {
    // This single, more robust regex handles multiple Planalto URL formats, including those with 'compilado'.
    const pattern = /(?:leis|decreto-lei|_ato\d+-\d+\/lei)\/((?:l|del|lei)\d+)/i;
    const match = url.match(pattern);

    if (match && match[1]) {
        const lawId = match[1].toLowerCase();
        const type = lawId.startsWith('del') ? 'Decreto-Lei' : 'Lei';
        const rawNumber = lawId.replace(/\D/g, ''); // Extract only digits
        const formattedNumber = parseInt(rawNumber, 10).toLocaleString('pt-BR');

        return {
            id: lawId,
            type: type,
            number: formattedNumber,
        };
    }

    return null; // Return null if no match is found
  };

  return (
    <div className="p-4">
      <h2 className="text-xl font-bold mb-4">Adicionar Nova Lei</h2>
      <form onSubmit={handleSubmit}>
        <input
          type="url"
          value={url}
          onChange={(e) => setUrl(e.target.value)}
          placeholder="Cole o link da lei do Planalto aqui"
          className="w-full p-2 border rounded mb-2 bg-transparent"
          required
        />
        <input
          type="text"
          value={customName}
          onChange={(e) => setCustomName(e.target.value)}
          placeholder="Nome personalizado (opcional)"
          className="w-full p-2 border rounded mb-2 bg-transparent"
        />
        <input
          type="text"
          value={customDescription}
          onChange={(e) => setCustomDescription(e.target.value)}
          placeholder="Descrição personalizada (opcional)"
          className="w-full p-2 border rounded mb-4 bg-transparent"
        />
        {error && <p className="text-red-500 mb-4">{error}</p>}
        <div className="flex justify-end gap-2">
          <button type="button" onClick={onClose} className="px-4 py-2 rounded bg-gray-300 dark:bg-gray-700">
            Cancelar
          </button>
          <button type="submit" className="px-4 py-2 rounded bg-primary text-white" disabled={loading}>
            {loading ? 'Adicionando...' : 'Adicionar'}
          </button>
        </div>
      </form>
    </div>
  );
};
