const axios = require('axios');
const iconv = require('iconv-lite');
const jschardet = require('jschardet');

/**
 * Fetches the raw HTML content from a given URL, handling character encoding.
 * @param {string} url The URL to scrape.
 * @returns {Promise<string>} A promise that resolves to the raw HTML string.
 */
async function fetchRawHtml(url) {
    console.log(`[SCRAPER] Fetching raw HTML from: ${url}`);
    try {
        const response = await axios.get(url, {
            responseType: 'arraybuffer',
            timeout: 30000, // 30 seconds timeout
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36'
            }
        });

        // Brazilian government sites often use windows-1252 encoding.
        // Auto-detection can fail, so we'll force it for stability.
        const html = iconv.decode(response.data, 'windows-1252');
        console.log(`[SCRAPER] Fetched and decoded with windows-1252.`);
        
        return html;
    } catch (error) {
        console.error(`[SCRAPER] Failed to fetch URL ${url}: ${error.message}`);
        // Re-throw the error to be handled by the Express route
        throw error;
    }
}

module.exports = { fetchRawHtml };